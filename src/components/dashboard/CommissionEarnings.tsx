"use client"

import React from 'react'

const earningsData = [
  {
    period: 'Today',
    amount: '₦5,000',
    label: 'Today'
  },
  {
    period: 'This week',
    amount: '₦10,250',
    label: 'This week'
  },
  {
    period: 'This month',
    amount: '₦345,250',
    label: 'This month'
  },
  {
    period: 'This year',
    amount: '₦478,938,250',
    label: 'This year'
  }
]

export default function CommissionEarnings() {
  return (
    <div className="bg-white rounded-xl py-[26px] px-7 border border-[#E9EBEE] h-full">
      {/* Header */}
      <h2 className="text-base font-normal text-[#4A4A68] mb-[19px] !font-dash">Commission Earnings</h2>

      {/* Earnings Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {earningsData.map((earning, index) => (
          <div key={index} className="text-left">
            <div className="text-xl font-bold text-black mb-1 !font-dash">
              {earning.amount}
            </div>
            <div className="text-sm text-[#4A4A68] !font-dash">
              {earning.label}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
