import Link from 'next/link'
import React from 'react'

import { cn } from '@/utils/classNames'
import { Button } from '../core/Button'
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '../core/Dialog'


export const LandingHeader = () => {
    return (
        <>
            <header className='bg-[#080D27]'>
                <section className=''>
                    <div className='flex items-center justify-between'>
                        <div className="flex items-center gap-4 lg:gap-10 xl:gap-[5.625rem]">
                            <Link href="/">

                            </Link>
                        </div>
                        <ul className='hidden md:flex items-center justify-center space-x-5 text-xs'>
                            <li>
                                <Link href='/'>Home</Link>
                            </li>
                            <li>
                                <Link href='/'>Plan</Link>
                            </li>
                            <li>
                                <Link href='/'>About us</Link>
                            </li>
                            <li>
                                <Link href='/'>FAQs</Link>
                            </li>
                            <li>
                                <Link href='/'>Contact us</Link>
                            </li>
                        </ul>
                        <ul className='hidden md:flex justify-end items-center space-x-5'>
                            <li>
                                <Link href='/'>login</Link>
                            </li>
                            <button className='flex justify-between items-center bg-white text-blue-950 bg rounded-full text-xs py-1 pl-5 pr-2 gap-[18px]'>
                                Get insurance
                                <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="15" fill="#032282" />
                                    <path d="M10.9168 19.6171C11.0334 19.6171 11.1501 19.5587 11.2084 19.5004L19.3751 11.3337C19.5501 11.1587 19.5501 10.9254 19.3751 10.7504C19.2001 10.5754 18.9084 10.5754 18.7334 10.7504L10.5668 18.9171C10.3918 19.0921 10.3918 19.3837 10.5668 19.5587C10.6834 19.6171 10.8001 19.6171 10.9168 19.6171Z" fill="white" />
                                    <path d="M19.0834 17.4585C19.3167 17.4585 19.55 17.2835 19.55 16.9919V11.0419C19.55 10.8085 19.375 10.5752 19.0834 10.5752H13.075C12.8417 10.5752 12.6084 10.7502 12.6084 11.0419C12.6084 11.3335 12.7834 11.5085 13.075 11.5085H18.6167V17.0502C18.6167 17.2835 18.85 17.4585 19.0834 17.4585Z" fill="white" />
                                </svg>

                            </button>
                        </ul>
                        {/* Mobile Menu */}
                        <div className="md:hidden">
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button className={cn("bg-white/10 px-5 py-2.5 rounded-full hover:bg-white/20 transition-colors", "font-display")}>
                                        Menu
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="w-full max-w-sm mx-auto bg-[#080D27] border-[#080D27] text-white rounded-lg shadow-xl">
                                    <div className="p-6">
                                        <div className="flex items-center justify-between mb-6">
                                            <h2 className="text-lg font-semibold text-white">Menu</h2>
                                            <DialogClose className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors">
                                                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </DialogClose>
                                        </div>

                                        <nav className="space-y-4">
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                                                    Home
                                                </Link>
                                            </DialogClose>
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                                                    Plan
                                                </Link>
                                            </DialogClose>
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                                                    About us
                                                </Link>
                                            </DialogClose>
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                                                    FAQs
                                                </Link>
                                            </DialogClose>
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                                                    Contact us
                                                </Link>
                                            </DialogClose>
                                        </nav>

                                        <div className="mt-6 pt-6 border-t border-white/20 space-y-4">
                                            <DialogClose asChild>
                                                <Link href='/' className="block px-4 py-2 text-sm font-medium text-white/80 hover:text-white transition-colors">
                                                    Login
                                                </Link>
                                            </DialogClose>
                                            <DialogClose asChild>
                                                <button className="w-full flex justify-center items-center bg-white text-[#080D27] rounded-full text-sm py-3 px-6 font-medium hover:bg-white/90 transition-colors">
                                                    Get insurance
                                                    <svg className="ml-2 w-5 h-5" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <circle cx="15" cy="15" r="15" fill="#032282" />
                                                        <path d="M11 15L15 19L19 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    </svg>
                                                </button>
                                            </DialogClose>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>
                    </div>

                </section>
            </header>
        </>
    )
}