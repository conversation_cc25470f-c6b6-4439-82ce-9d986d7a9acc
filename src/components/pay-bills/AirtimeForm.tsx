"use client"

import React from 'react'
import { Input } from '@/components/core/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select'
import { Button } from '@/components/core/Button'

interface AirtimeFormProps {
  formData: {
    phoneNumber: string
    network: string
    amount: string
    email: string
  }
  onInputChange: (field: string, value: string) => void
  onProceed: () => void
  onCancel: () => void
}

export default function AirtimeForm({ formData, onInputChange, onProceed, onCancel }: AirtimeFormProps) {
  const quickAmounts = ['100', '200', '500', '1000', '2000', '5000']

  const handleQuickAmount = (amount: string) => {
    onInputChange('amount', amount)
  }

  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-[20px] p-7">
      <div className="text-left  mb-8">
        <h2 className="text-xl font-semibold text-black mb-2">Buy Airtime</h2>
        <p className="text-[#4A4A68] text-xs">Top up airtime in just 35secs</p>
      </div>

      <div className="space-y-6">
        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Phone Number
          </label>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phoneNumber}
            onChange={(e) => onInputChange('phoneNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Network Provider */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Network Provider
          </label>
          <Select value={formData.network} onValueChange={(value) => onInputChange('network', value)}>
            <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
              <SelectValue placeholder="Select network" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="mtn">MTN</SelectItem>
              <SelectItem value="glo">Glo</SelectItem>
              <SelectItem value="airtel">Airtel</SelectItem>
              <SelectItem value="9mobile">9Mobile</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Quick Amount Selection */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Quick Amount
          </label>
          <div className="grid grid-cols-3 gap-2 mb-4">
            {quickAmounts.map((amount) => (
              <button
                key={amount}
                onClick={() => handleQuickAmount(amount)}
                className={`py-2 px-3 text-sm border rounded-lg transition-colors ${formData.amount === amount
                  ? 'bg-green-600 text-white border-green-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-green-600'
                  }`}
              >
                ₦{amount}
              </button>
            ))}
          </div>
        </div>

        {/* Custom Amount */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Amount
          </label>
          <Input
            type="number"
            placeholder="Enter amount"
            value={formData.amount}
            onChange={(e) => onInputChange('amount', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
            min="50"
          />
        </div>

        {/* Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Email (Optional)
          </label>
          <Input
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={(e) => onInputChange('email', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 pt-4">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            disabled={!formData.phoneNumber || !formData.network || !formData.amount}
          >
            Proceed
          </Button>
        </div>
      </div>
    </div>
  )
}
