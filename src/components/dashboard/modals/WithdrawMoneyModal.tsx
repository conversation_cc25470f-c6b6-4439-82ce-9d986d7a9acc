"use client"

import React, { useState } from 'react'
import {
  <PERSON>alog,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogBody
} from '@/components/core/Dialog'
import { Button } from '@/components/core/Button'
import { Input } from '@/components/core/Input'

interface WithdrawMoneyModalProps {
  isOpen: boolean
  onClose: () => void
}

type WithdrawStep = 'details' | 'otp'

export default function WithdrawMoneyModal({ isOpen, onClose }: WithdrawMoneyModalProps) {
  const [step, setStep] = useState<WithdrawStep>('details')
  const [formData, setFormData] = useState({
    bank: '',
    accountNumber: '',
    accountName: '',
    description: '',
    amount: ''
  })
  const [otp, setOtp] = useState(['', '', '', '', '', ''])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newOtp = [...otp]
      newOtp[index] = value
      setOtp(newOtp)

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`)
        nextInput?.focus()
      }
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`)
      prevInput?.focus()
    }
  }

  const handleClose = () => {
    setStep('details')
    setFormData({
      bank: '',
      accountNumber: '',
      accountName: '',
      description: '',
      amount: ''
    })
    setOtp(['', '', '', '', '', ''])
    onClose()
  }

  const handleConfirmTransfer = () => {
    // Validate form data
    if (!formData.bank || !formData.accountNumber || !formData.accountName) {
      alert('Please fill in all required fields')
      return
    }
    setStep('otp')
  }

  const handleOtpSubmit = () => {
    const otpValue = otp.join('')
    if (otpValue.length !== 6) {
      alert('Please enter complete OTP')
      return
    }

    // Handle OTP verification and withdrawal processing
    console.log('Processing withdrawal with OTP:', otpValue)
    console.log('Withdrawal details:', formData)
    handleClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="w-full max-w-md mx-4 bg-white rounded-2xl border-0 p-0"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6">
          <DialogHeader className="flex flex-row items-center justify-between bg-transparent px-0 py-0 mb-4">
            <DialogTitle className="text-lg font-medium text-black">
              {step === 'details' ? 'Withdraw money' : 'Enter OTP'}
            </DialogTitle>
            <DialogClose>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </DialogClose>
          </DialogHeader>

          <DialogBody className="px-0 py-0">
            {step === 'details' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-4">
                  Enter your withdrawal destination
                </p>

                {/* Withdrawal Form */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Amount
                    </label>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      value={formData.amount}
                      onChange={(e) => handleInputChange('amount', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Bank
                    </label>
                    <Input
                      type="text"
                      placeholder="Enter meter number"
                      value={formData.bank}
                      onChange={(e) => handleInputChange('bank', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Account Number
                    </label>
                    <Input
                      type="text"
                      placeholder="Enter meter number"
                      value={formData.accountNumber}
                      onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Account Name
                    </label>
                    <Input
                      type="text"
                      placeholder="[display account details]"
                      value={formData.accountName}
                      onChange={(e) => handleInputChange('accountName', e.target.value)}
                      className="w-full text-gray-500"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      placeholder="Enter meter number"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      className="w-full min-h-[80px] px-3 py-2 border border-gray-300 rounded-lg text-sm resize-none focus:outline-none focus:ring-2 focus:ring-[#105230] focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Confirm Button */}
                <Button
                  onClick={handleConfirmTransfer}
                  className="w-full bg-[#105230] hover:bg-[#0d4228] text-white py-3 rounded-lg font-medium mt-6"
                >
                  Confirm Transfer
                </Button>
              </div>
            )}

            {step === 'otp' && (
              <div className="space-y-6">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-2">
                    Enter the 6-digits OTP sent to ******<EMAIL> to confirm and complete withdrawal transaction
                  </p>
                </div>

                {/* OTP Input */}
                <div className="flex justify-center gap-3">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      id={`otp-${index}`}
                      type="text"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      className="w-12 h-12 text-center text-lg font-medium border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#105230] focus:border-transparent"
                    />
                  ))}
                </div>

                {/* Confirm Button */}
                <Button
                  onClick={handleOtpSubmit}
                  className="w-full bg-[#105230] hover:bg-[#0d4228] text-white py-3 rounded-lg font-medium"
                >
                  Confirm Transfer
                </Button>

                {/* Back Button */}
                <Button
                  onClick={() => setStep('details')}
                  variant="outline"
                  className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Back
                </Button>
              </div>
            )}
          </DialogBody>
        </div>
      </DialogContent>
    </Dialog>
  )
}
