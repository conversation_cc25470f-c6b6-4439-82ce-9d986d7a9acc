'use client'

import React from 'react'


import VendHero from "./misc/components/VendHero";
import WhatsappIcon from "./misc/icons/footer/Whatsapp";
import { motion } from "framer-motion"

export default function Home() {
  const [openFaq, setOpenFaq] = React.useState<number | null>(0)

  const faqData = [
    {
      question: "How do I fund my Vendboss wallet?",
      answer:
        "You can fund your wallet using bank transfer, debit card, or USSD. We'll provide you a unique account number for easy top-ups.",
    },
    {
      question: "What services can I pay for on Vendboss?",
      answer:
        "You can pay for airtime, data bundles, electricity bills, cable TV subscriptions, and many other utility services directly through WhatsApp.",
    },
    {
      question: "What happens if my transaction fails?",
      answer:
        "If your transaction fails, your money will be automatically refunded to your wallet within minutes. You can then retry the transaction.",
    },
    {
      question: "Is Vendboss safe to use?",
      answer:
        "Yes, Vendboss uses bank-grade security and encryption to protect all your transactions and personal information.",
    },
    {
      question: "Can I earn money as an agent/reseller?",
      answer:
        "Yes, you can become a Vendboss agent and earn commissions on every transaction you facilitate for customers.",
    },
    {
      question: "How do I become a Vendboss agent?",
      answer:
        "Contact our support team through WhatsApp to learn about our agent program and start earning commissions today.",
    },
  ]
  return (
    <div className="bg-white min-h-screen">
      <VendHero />

      <section className="py-16 container mx-auto">
        <div className="mb-16">
          <div className="relative overflow-hidden whitespace-nowrap">
            {/* Left shadow overlay */}
            <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-white to-transparent z-10 pointer-events-none"></div>
            {/* Right shadow overlay */}
            <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-white to-transparent z-10 pointer-events-none"></div>

            <div className="inline-flex animate-marquee">
              <div className="flex items-center space-x-12 mx-6">
                <img src="/images/partners-slide/mtn.svg" alt="Partner 1" className="h-10  opacity-80" />
                <img src="/images/partners-slide/ikeja-elec.svg" alt="Partner 2" className="h-10  opacity-80" />
                <img src="/images/partners-slide/airtel.svg" alt="Partner 3" className="h-10  opacity-80" />
                <img src="/images/partners-slide/eko-elec.svg" alt="Partner 4" className="h-10  opacity-80" />
                <img src="/images/partners-slide/dstv.svg" alt="Partner 5" className="h-10  opacity-80" />
                <img src="/images/partners-slide/ib-elec.svg" alt="Partner 6" className="h-10  opacity-80" />
                <img src="/images/partners-slide/port-elec.svg" alt="Partner 7" className="h-10  opacity-80" />
                <img src="/images/partners-slide/go-tv.svg" alt="Partner 8" className="h-10  opacity-80" />
                <img src="/images/partners-slide/glo.svg" alt="Partner 9" className="h-10  opacity-80" />
              </div>
              {/* Duplicate for seamless loop */}
              <div className="flex items-center space-x-12 mx-6">
                <img src="/images/partners-slide/mtn.svg" alt="Partner 1" className="h-10  opacity-80" />
                <img src="/images/partners-slide/ikeja-elec.svg" alt="Partner 2" className="h-10  opacity-80" />
                <img src="/images/partners-slide/airtel.svg" alt="Partner 3" className="h-10  opacity-80" />
                <img src="/images/partners-slide/eko-elec.svg" alt="Partner 4" className="h-10  opacity-80" />
                <img src="/images/partners-slide/dstv.svg" alt="Partner 5" className="h-10  opacity-80" />
                <img src="/images/partners-slide/ib-elec.svg" alt="Partner 6" className="h-10  opacity-80" />
                <img src="/images/partners-slide/port-elec.svg" alt="Partner 7" className="h-10  opacity-80" />
                <img src="/images/partners-slide/go-tv.svg" alt="Partner 8" className="h-10  opacity-80" />
                <img src="/images/partners-slide/glo.svg" alt="Partner 9" className="h-10  opacity-80" />
              </div>
            </div>
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: false, amount: 0.3 }}
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        >
          <div className="bg-[#F1F0E4] rounded-3xl px-8 lg:px-12">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                viewport={{ once: false, amount: 0.3 }}
                className="space-y-6"
              >
                <h2 className="text-4xl font-medium text-[#101828] leading-tight max-w-[553px]">
                  Perform transaction right from your WhatsApp
                </h2>
                <p className="text-base max-w-[519px] text-black leading-relaxed">
                  Top up airtime, buy data, pay electricity bills, and more—without leaving your WhatsApp chat.
                  It&apos;s fast, easy, and always available with VendDocs.
                </p>
                <button className="bg-[#105230] hover:bg-green-700 text-white px-8 py-4 rounded-full font-medium text-base transition-colors duration-200 flex items-center gap-3">
                  Get started now
                  <WhatsappIcon />
                </button>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                viewport={{ once: false, amount: 0.3 }}
                className="flex justify-start lg:justify-start"
              >
                <div className="relative">
                  <img
                    src="/images/get-started-setion.svg"
                    alt="WhatsApp interface showing VendDocs transaction"
                    className="w-[424px] h-[518px]"
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </section>

      <section className="py-20 bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: false, amount: 0.3 }}
          className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"
        >
          <div className="text-center mb-12">
            <h2 className="text-4xl font-medium text-[#101828] mb-4">Questions you might have ?</h2>
            <p className="text-black max-w-[400px] mx-auto">
              From payments to security and refunds, see how VendDocs keeps things simple and secure.
            </p>
          </div>

          <div className="space-y-2">
            {faqData.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: false, amount: 0.3 }}
                className={`${openFaq === index ? "bg-[#E7EEEA]" : "bg-white"} border-b border-[#105230] overflow-hidden`}
              >
                <button
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-[#E7EEEA] transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <span className="text-3xl font-semibold text-[#91AFA0]">{String(index + 1).padStart(2, "0")}</span>
                    <span className="text-base font-medium text-gray-900">{faq.question}</span>
                  </div>
                  <div className="">
                    {openFaq === index ? (
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="12" fill="#B5C9BF" />
                        <path
                          d="M16.9351 12.8702H7.64508C7.32767 12.8702 7.06445 12.607 7.06445 12.2896C7.06445 11.9722 7.32767 11.709 7.64508 11.709H16.9351C17.2525 11.709 17.5157 11.9722 17.5157 12.2896C17.5157 12.607 17.2525 12.8702 16.9351 12.8702Z"
                          fill="#0B3A22"
                        />
                      </svg>
                    ) : (
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="12" fill="#105230" />
                        <path
                          d="M17.0321 12.9679H7.74178C7.42436 12.9679 7.16113 12.7047 7.16113 12.3873C7.16113 12.0699 7.42436 11.8066 7.74178 11.8066H17.0321C17.3495 11.8066 17.6127 12.0699 17.6127 12.3873C17.6127 12.7047 17.3495 12.9679 17.0321 12.9679Z"
                          fill="white"
                        />
                        <path
                          d="M12.3873 17.6127C12.0699 17.6127 11.8066 17.3495 11.8066 17.0321V7.74178C11.8066 7.42436 12.0699 7.16113 12.3873 7.16113C12.7047 7.16113 12.9679 7.42436 12.9679 7.74178V17.0321C12.9679 17.3495 12.7047 17.6127 12.3873 17.6127Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </div>
                </button>
                {openFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 pb-6"
                  >
                    <div className="pl-12">
                      <p className="text-sm leading-relaxed text-black">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      <footer className="bg-white">
        {/* Lifestyle images marquee */}
        <div className="relative overflow-hidden">
          <div className="inline-flex animate-marquee-slow">
            <div className="flex">
              <img
                src="/images/footer-images/footerimg-1.svg"
                alt="Professional working"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-2.svg"
                alt="Business meeting"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-3.svg"
                alt="Mobile payments"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-4.svg"
                alt="Team collaboration"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-5.svg"
                alt="Multi-device work"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-6.svg"
                alt="Casual business meeting"
                className="h-48 w-72 object-cover"
              />
            </div>
            {/* Duplicate for seamless loop */}
            <div className="flex">
              <img
                src="/images/footer-images/footerimg-1.svg"
                alt="Professional working"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-2.svg"
                alt="Business meeting"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-3.svg"
                alt="Mobile payments"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-4.svg"
                alt="Team collaboration"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-5.svg"
                alt="Multi-device work"
                className="h-48 w-72 object-cover"
              />
              <img
                src="/images/footer-images/footerimg-6.svg"
                alt="Casual business meeting"
                className="h-48 w-72 object-cover"
              />
            </div>
          </div>
        </div>

        {/* Footer content */}
        <div className="bg-[#0F4A2A] text-white py-8 -mt-[6px]">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center gap-2 mb-4 md:mb-0">
                <svg width="128" height="42" viewBox="0 0 128 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect y="17.417" width="7.16667" height="7.16667" rx="3.58333" fill="white" />
                  <path d="M17.2271 27L12.3155 13.6222H14.7809L18.6222 24.6493L22.5209 13.6222H24.948L20.0173 27H17.2271ZM28.7379 27V13.6222H37.3762V15.476H31.0313V19.3173H36.8028V21.1329H31.0313V25.1462H37.3762V27H28.7379ZM41.8888 27V13.6222H44.1821L50.6226 23.2924V13.6222H52.9159V27H50.6226L44.1821 17.3489V27H41.8888ZM57.7084 27V13.6222H62.0276C63.5819 13.6222 64.8624 13.8961 65.8689 14.444C66.8882 14.9919 67.6399 15.769 68.124 16.7756C68.6209 17.7693 68.8693 18.9542 68.8693 20.3302C68.8693 21.6807 68.6209 22.8593 68.124 23.8658C67.6399 24.8596 66.8882 25.6304 65.8689 26.1782C64.8624 26.7261 63.5819 27 62.0276 27H57.7084ZM60.0018 25.0507H61.9129C63.0723 25.0507 63.9833 24.8659 64.6458 24.4964C65.321 24.1142 65.7988 23.5727 66.0791 22.872C66.3722 22.1585 66.5187 21.3113 66.5187 20.3302C66.5187 19.3364 66.3722 18.4892 66.0791 17.7884C65.7988 17.075 65.321 16.5271 64.6458 16.1449C63.9833 15.7499 63.0723 15.5524 61.9129 15.5524H60.0018V25.0507Z" fill="#EABB27" />
                  <path d="M73.2108 27V13.6222H78.4473C79.3901 13.6222 80.1737 13.7687 80.798 14.0618C81.435 14.3548 81.9128 14.7625 82.2313 15.2849C82.5498 15.7945 82.7091 16.387 82.7091 17.0622C82.7091 17.7375 82.5625 18.3044 82.2695 18.7631C81.9765 19.2218 81.5879 19.5721 81.1037 19.8142C80.6323 20.0563 80.1163 20.2028 79.5557 20.2538L79.8424 20.0436C80.4412 20.0563 80.9763 20.2156 81.4477 20.5213C81.9319 20.8271 82.3141 21.2284 82.5944 21.7253C82.8747 22.2095 83.0148 22.751 83.0148 23.3498C83.0148 24.0505 82.8428 24.6812 82.4988 25.2418C82.1676 25.7896 81.6771 26.2228 81.0273 26.5413C80.3775 26.8471 79.5812 27 78.6384 27H73.2108ZM75.5042 25.1271H78.2944C79.0461 25.1271 79.6322 24.9487 80.0526 24.592C80.4731 24.2353 80.6833 23.7384 80.6833 23.1013C80.6833 22.4643 80.4667 21.961 80.0335 21.5916C79.6003 21.2093 79.0079 21.0182 78.2562 21.0182H75.5042V25.1271ZM75.5042 19.2791H78.1224C78.8614 19.2791 79.422 19.1135 79.8042 18.7822C80.1864 18.4382 80.3775 17.9668 80.3775 17.368C80.3775 16.7819 80.1864 16.3233 79.8042 15.992C79.422 15.648 78.855 15.476 78.1033 15.476H75.5042V19.2791ZM93.4099 27.2293C92.1231 27.2293 90.9828 26.9427 89.9891 26.3693C89.008 25.7833 88.2372 24.9742 87.6766 23.9422C87.116 22.8975 86.8357 21.6871 86.8357 20.3111C86.8357 18.9479 87.116 17.7502 87.6766 16.7182C88.2372 15.6735 89.008 14.8581 89.9891 14.272C90.9828 13.6859 92.1231 13.3929 93.4099 13.3929C94.7222 13.3929 95.8753 13.6859 96.8691 14.272C97.8628 14.8581 98.6336 15.6735 99.1815 16.7182C99.7294 17.7502 100.003 18.9479 100.003 20.3111C100.003 21.6871 99.7294 22.8975 99.1815 23.9422C98.6336 24.9742 97.8628 25.7833 96.8691 26.3693C95.8753 26.9427 94.7222 27.2293 93.4099 27.2293ZM93.4291 25.1653C94.2827 25.1653 95.028 24.9742 95.6651 24.592C96.3021 24.197 96.7926 23.6364 97.1366 22.9102C97.4806 22.184 97.6526 21.3176 97.6526 20.3111C97.6526 19.3046 97.4806 18.4382 97.1366 17.712C96.7926 16.9858 96.3021 16.4316 95.6651 16.0493C95.028 15.6544 94.2827 15.4569 93.4291 15.4569C92.5754 15.4569 91.8301 15.6544 91.1931 16.0493C90.556 16.4316 90.0591 16.9858 89.7024 17.712C89.3584 18.4382 89.1864 19.3046 89.1864 20.3111C89.1864 21.3176 89.3584 22.184 89.7024 22.9102C90.0591 23.6364 90.556 24.197 91.1931 24.592C91.8301 24.9742 92.5754 25.1653 93.4291 25.1653ZM108.837 27.2293C107.869 27.2293 107.009 27.0573 106.257 26.7133C105.506 26.3693 104.92 25.8788 104.499 25.2418C104.079 24.6047 103.862 23.8467 103.849 22.9676H106.276C106.276 23.4135 106.378 23.8148 106.582 24.1716C106.799 24.5156 107.092 24.7895 107.461 24.9933C107.844 25.1972 108.302 25.2991 108.837 25.2991C109.296 25.2991 109.691 25.229 110.022 25.0889C110.366 24.936 110.627 24.7258 110.806 24.4582C110.997 24.1779 111.092 23.853 111.092 23.4836C111.092 23.0631 110.991 22.7191 110.787 22.4516C110.596 22.1713 110.328 21.9356 109.984 21.7444C109.64 21.5533 109.245 21.3877 108.799 21.2476C108.353 21.0947 107.882 20.9354 107.385 20.7698C106.327 20.413 105.531 19.9607 104.996 19.4129C104.461 18.8523 104.193 18.107 104.193 17.1769C104.193 16.3997 104.378 15.7308 104.748 15.1702C105.117 14.6096 105.633 14.1764 106.296 13.8707C106.971 13.5521 107.748 13.3929 108.627 13.3929C109.519 13.3929 110.296 13.5521 110.959 13.8707C111.634 14.1892 112.163 14.6351 112.545 15.2084C112.94 15.769 113.144 16.4443 113.156 17.2342H110.71C110.698 16.903 110.608 16.5972 110.443 16.3169C110.277 16.0239 110.035 15.7881 109.716 15.6098C109.411 15.4187 109.035 15.3231 108.589 15.3231C108.207 15.3104 107.863 15.3741 107.557 15.5142C107.264 15.6416 107.028 15.8327 106.85 16.0876C106.684 16.3296 106.601 16.6354 106.601 17.0049C106.601 17.3616 106.678 17.661 106.831 17.9031C106.996 18.1324 107.232 18.3299 107.538 18.4956C107.844 18.6484 108.2 18.795 108.608 18.9351C109.016 19.0753 109.462 19.2281 109.946 19.3938C110.608 19.6104 111.207 19.8779 111.742 20.1964C112.29 20.5022 112.723 20.9036 113.042 21.4004C113.36 21.8973 113.52 22.5407 113.52 23.3307C113.52 24.0187 113.341 24.6621 112.984 25.2609C112.628 25.847 112.105 26.3247 111.417 26.6942C110.729 27.051 109.869 27.2293 108.837 27.2293ZM122.343 27.2293C121.375 27.2293 120.515 27.0573 119.763 26.7133C119.011 26.3693 118.425 25.8788 118.005 25.2418C117.584 24.6047 117.368 23.8467 117.355 22.9676H119.782C119.782 23.4135 119.884 23.8148 120.088 24.1716C120.304 24.5156 120.597 24.7895 120.967 24.9933C121.349 25.1972 121.808 25.2991 122.343 25.2991C122.801 25.2991 123.196 25.229 123.528 25.0889C123.872 24.936 124.133 24.7258 124.311 24.4582C124.502 24.1779 124.598 23.853 124.598 23.4836C124.598 23.0631 124.496 22.7191 124.292 22.4516C124.101 22.1713 123.833 21.9356 123.489 21.7444C123.145 21.5533 122.751 21.3877 122.305 21.2476C121.859 21.0947 121.387 20.9354 120.89 20.7698C119.833 20.413 119.037 19.9607 118.501 19.4129C117.966 18.8523 117.699 18.107 117.699 17.1769C117.699 16.3997 117.884 15.7308 118.253 15.1702C118.623 14.6096 119.139 14.1764 119.801 13.8707C120.476 13.5521 121.253 13.3929 122.133 13.3929C123.024 13.3929 123.802 13.5521 124.464 13.8707C125.139 14.1892 125.668 14.6351 126.05 15.2084C126.445 15.769 126.649 16.4443 126.662 17.2342H124.216C124.203 16.903 124.114 16.5972 123.948 16.3169C123.783 16.0239 123.54 15.7881 123.222 15.6098C122.916 15.4187 122.54 15.3231 122.094 15.3231C121.712 15.3104 121.368 15.3741 121.062 15.5142C120.769 15.6416 120.534 15.8327 120.355 16.0876C120.19 16.3296 120.107 16.6354 120.107 17.0049C120.107 17.3616 120.183 17.661 120.336 17.9031C120.502 18.1324 120.737 18.3299 121.043 18.4956C121.349 18.6484 121.706 18.795 122.113 18.9351C122.521 19.0753 122.967 19.2281 123.451 19.3938C124.114 19.6104 124.713 19.8779 125.248 20.1964C125.796 20.5022 126.229 20.9036 126.547 21.4004C126.866 21.8973 127.025 22.5407 127.025 23.3307C127.025 24.0187 126.847 24.6621 126.49 25.2609C126.133 25.847 125.611 26.3247 124.923 26.6942C124.235 27.051 123.375 27.2293 122.343 27.2293Z" fill="#F5F5F5" />
                </svg>

              </div>
              <div className="text-center md:text-right">
                <p className="text-sm">
                  Buy on WhatsApp via: <span className="font-medium text-[#02BA5C]">+2349000000000</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
