"use client"

import React from 'react'
import { Input } from '@/components/core/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select'
import { Button } from '@/components/core/Button'

interface ElectricityFormProps {
  formData: {
    meterNumber: string
    meterType: string
    location: string
    amount: string
    phoneNumber: string
    email: string
  }
  onInputChange: (field: string, value: string) => void
  onProceed: () => void
  onCancel: () => void
}

export default function ElectricityForm({ formData, onInputChange, onProceed, onCancel }: ElectricityFormProps) {
  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-[20px] p-7">
      <div className="text-left mb-8">
        <h2 className="text-xl font-semibold text-black mb-2">Buy Electricity</h2>
        <p className="text-[#4A4A68] text-xs">Pay for electricity in just 35secs</p>
      </div>

      <div className="space-y-6">
        {/* Meter Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Meter Number
          </label>
          <Input
            type="text"
            placeholder="Enter meter number"
            value={formData.meterNumber}
            onChange={(e) => onInputChange('meterNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Account/Meter Type */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Account/Meter Type
          </label>
          <Select value={formData.meterType} onValueChange={(value) => onInputChange('meterType', value)}>
            <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
              <SelectValue placeholder="Select meter type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="prepaid">Prepaid</SelectItem>
              <SelectItem value="postpaid">Postpaid</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* State/Location */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            State/Location
          </label>
          <Select value={formData.location} onValueChange={(value) => onInputChange('location', value)}>
            <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
              <SelectValue placeholder="Select location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lagos">Lagos</SelectItem>
              <SelectItem value="abuja">Abuja</SelectItem>
              <SelectItem value="kano">Kano</SelectItem>
              <SelectItem value="port-harcourt">Port Harcourt</SelectItem>
              <SelectItem value="ibadan">Ibadan</SelectItem>
              <SelectItem value="kaduna">Kaduna</SelectItem>
              <SelectItem value="jos">Jos</SelectItem>
              <SelectItem value="enugu">Enugu</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Amount
          </label>
          <Input
            type="number"
            placeholder="Enter amount"
            value={formData.amount}
            onChange={(e) => onInputChange('amount', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
            min="100"
          />
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Phone Number
          </label>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phoneNumber}
            onChange={(e) => onInputChange('phoneNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Email (Optional)
          </label>
          <Input
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={(e) => onInputChange('email', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 pt-4">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            disabled={!formData.meterNumber || !formData.meterType || !formData.location || !formData.amount || !formData.phoneNumber}
          >
            Proceed
          </Button>
        </div>
      </div>
    </div>
  )
}
