"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/utils/classNames'

interface NavigationItem {
  name: string
  href: string
  icon: React.ReactNode
  badge?: number
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>
    )
  },
  {
    name: 'Pay Bills',
    href: '/pay-bills',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    )
  },
  {
    name: 'Notifications',
    href: '/notifications',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.73 21a2 2 0 01-3.46 0" />
      </svg>
    ),
    badge: 3 // Example notification count
  },
  {
    name: 'FAQ',
    href: '/faq',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
]

export default function MobileBottomNav() {
  const pathname = usePathname()

  return (
    <>
      {/* Bottom Navigation - Mobile Only */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-50">
        {/* Background with blur effect */}
        <div className="bg-white/95 backdrop-blur-lg border-t border-gray-200 shadow-lg">
          <div className="flex items-center justify-around px-2 py-2">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href || (pathname === '/' && item.href === '/dashboard')

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'flex flex-col items-center justify-center px-3 py-2 rounded-xl transition-all duration-300 min-w-[60px] relative',
                    isActive
                      ? 'text-[#105230] bg-[#105230]/10 scale-105'
                      : 'text-gray-500 hover:text-[#105230] hover:bg-gray-50 active:scale-95'
                  )}
                >
                  {/* Active indicator dot */}
                  {isActive && (
                    <div className="absolute -top-1 w-1 h-1 bg-[#105230] rounded-full animate-pulse" />
                  )}

                  {/* Icon with badge */}
                  <div className={cn(
                    'relative transition-all duration-300',
                    isActive ? 'scale-110' : 'scale-100'
                  )}>
                    {item.icon}
                    {/* Notification badge */}
                    {item.badge && item.badge > 0 && (
                      <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-medium animate-pulse">
                        {item.badge > 99 ? '99+' : item.badge}
                      </div>
                    )}
                  </div>

                  {/* Label */}
                  <span className={cn(
                    'text-xs font-medium mt-1 transition-all duration-300',
                    isActive ? 'font-semibold' : 'font-normal'
                  )}>
                    {item.name}
                  </span>

                  {/* Active background glow */}
                  {isActive && (
                    <div className="absolute inset-0 bg-[#105230]/5 rounded-xl -z-10 animate-pulse" />
                  )}
                </Link>
              )
            })}
          </div>
        </div>

        {/* Safe area padding for devices with home indicator */}
        <div className="bg-white/95 backdrop-blur-lg h-safe-area-inset-bottom" />
      </div>

      {/* Spacer to prevent content from being hidden behind bottom nav */}
      <div className="md:hidden md:h-20" />
    </>
  )
}
