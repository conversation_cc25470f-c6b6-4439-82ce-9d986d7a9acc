"use client"

import React from 'react'
import { Input } from '@/components/core/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select'
import { Button } from '@/components/core/Button'

interface GenericServiceFormProps {
  serviceType: string
  formData: {
    serviceId: string
    provider: string
    amount: string
    phoneNumber: string
    email: string
  }
  onInputChange: (field: string, value: string) => void
  onProceed: () => void
  onCancel: () => void
}

export default function GenericServiceForm({
  serviceType,
  formData,
  onInputChange,
  onProceed,
  onCancel
}: GenericServiceFormProps) {

  const getServiceConfig = () => {
    switch (serviceType) {
      case 'Education':
        return {
          title: 'Pay Education',
          subtitle: 'Pay for education services in just 35secs',
          serviceIdLabel: 'Student ID / Reference',
          serviceIdPlaceholder: 'Enter student ID or reference',
          providers: [
            { value: 'waec', label: 'WAEC' },
            { value: 'neco', label: 'NECO' },
            { value: 'jamb', label: 'JAMB' },
            { value: 'nabteb', label: 'NABTEB' }
          ]
        }
      case 'Waste Bill':
        return {
          title: 'Pay Waste Bill',
          subtitle: 'Pay for waste management in just 35secs',
          serviceIdLabel: 'Account Number',
          serviceIdPlaceholder: 'Enter account number',
          providers: [
            { value: 'lawma', label: 'LAWMA' },
            { value: 'aepb', label: 'AEPB' },
            { value: 'kswb', label: 'KSWB' }
          ]
        }
      case 'Betting':
        return {
          title: 'Fund Betting Account',
          subtitle: 'Fund your betting account in just 35secs',
          serviceIdLabel: 'User ID',
          serviceIdPlaceholder: 'Enter user ID',
          providers: [
            { value: 'bet9ja', label: 'Bet9ja' },
            { value: 'sportybet', label: 'SportyBet' },
            { value: 'betking', label: 'BetKing' },
            { value: 'nairabet', label: 'NairaBet' }
          ]
        }
      case 'Water Bill':
        return {
          title: 'Pay Water Bill',
          subtitle: 'Pay for water services in just 35secs',
          serviceIdLabel: 'Account Number',
          serviceIdPlaceholder: 'Enter account number',
          providers: [
            { value: 'lagos-water', label: 'Lagos Water Corporation' },
            { value: 'abuja-water', label: 'FCT Water Board' },
            { value: 'kano-water', label: 'Kano State Water Board' }
          ]
        }
      default:
        return {
          title: `Pay ${serviceType}`,
          subtitle: `Pay for ${serviceType.toLowerCase()} in just 35secs`,
          serviceIdLabel: 'Service ID',
          serviceIdPlaceholder: 'Enter service ID',
          providers: []
        }
    }
  }

  const config = getServiceConfig()

  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-[20px] p-7">
      <div className="text-left  mb-8">
        <h2 className="text-xl font-semibold text-black mb-2">{config.title}</h2>
        <p className="text-[#4A4A68] text-xs">{config.subtitle}</p>
      </div>

      <div className="space-y-6">
        {/* Service ID */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            {config.serviceIdLabel}
          </label>
          <Input
            type="text"
            placeholder={config.serviceIdPlaceholder}
            value={formData.serviceId}
            onChange={(e) => onInputChange('serviceId', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Provider */}
        {config.providers.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Provider
            </label>
            <Select value={formData.provider} onValueChange={(value) => onInputChange('provider', value)}>
              <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {config.providers.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Amount
          </label>
          <Input
            type="number"
            placeholder="Enter amount"
            value={formData.amount}
            onChange={(e) => onInputChange('amount', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
            min="100"
          />
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Phone Number
          </label>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phoneNumber}
            onChange={(e) => onInputChange('phoneNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Email (Optional)
          </label>
          <Input
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={(e) => onInputChange('email', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 pt-4">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            disabled={!formData.serviceId || !formData.amount || !formData.phoneNumber}
          >
            Proceed
          </Button>
        </div>
      </div>
    </div>
  )
}
