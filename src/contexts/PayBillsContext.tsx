"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'

export interface ElectricityFormData {
  meterNumber: string
  meterType: string
  location: string
  amount: string
  phoneNumber: string
  email: string
}

export interface AirtimeFormData {
  phoneNumber: string
  network: string
  amount: string
  email: string
}

export interface CableTVFormData {
  decoderNumber: string
  provider: string
  package: string
  phoneNumber: string
  email: string
}

export interface InternetFormData {
  accountNumber: string
  provider: string
  package: string
  phoneNumber: string
  email: string
}

export interface GenericFormData {
  serviceId: string
  provider: string
  amount: string
  phoneNumber: string
  email: string
}

export type ServiceType = 'Electricity' | 'Airtime' | 'Cable TV' | 'Internet' | 'Education' | 'Waste Bill' | 'Betting' | 'Water Bill'

interface PayBillsContextType {
  activeService: ServiceType
  setActiveService: (service: ServiceType) => void
  
  electricityData: ElectricityFormData
  setElectricityData: React.Dispatch<React.SetStateAction<ElectricityFormData>>
  
  airtimeData: AirtimeFormData
  setAirtimeData: React.Dispatch<React.SetStateAction<AirtimeFormData>>
  
  cableTVData: CableTVFormData
  setCableTVData: React.Dispatch<React.SetStateAction<CableTVFormData>>
  
  internetData: InternetFormData
  setInternetData: React.Dispatch<React.SetStateAction<InternetFormData>>
  
  genericData: GenericFormData
  setGenericData: React.Dispatch<React.SetStateAction<GenericFormData>>
  
  getCurrentFormData: () => any
  resetCurrentFormData: () => void
}

const PayBillsContext = createContext<PayBillsContextType | undefined>(undefined)

export function PayBillsProvider({ children }: { children: ReactNode }) {
  const [activeService, setActiveService] = useState<ServiceType>('Electricity')
  
  const [electricityData, setElectricityData] = useState<ElectricityFormData>({
    meterNumber: '',
    meterType: '',
    location: '',
    amount: '',
    phoneNumber: '',
    email: ''
  })

  const [airtimeData, setAirtimeData] = useState<AirtimeFormData>({
    phoneNumber: '',
    network: '',
    amount: '',
    email: ''
  })

  const [cableTVData, setCableTVData] = useState<CableTVFormData>({
    decoderNumber: '',
    provider: '',
    package: '',
    phoneNumber: '',
    email: ''
  })

  const [internetData, setInternetData] = useState<InternetFormData>({
    accountNumber: '',
    provider: '',
    package: '',
    phoneNumber: '',
    email: ''
  })

  const [genericData, setGenericData] = useState<GenericFormData>({
    serviceId: '',
    provider: '',
    amount: '',
    phoneNumber: '',
    email: ''
  })

  const getCurrentFormData = () => {
    switch (activeService) {
      case 'Electricity':
        return electricityData
      case 'Airtime':
        return airtimeData
      case 'Cable TV':
        return cableTVData
      case 'Internet':
        return internetData
      default:
        return genericData
    }
  }

  const resetCurrentFormData = () => {
    switch (activeService) {
      case 'Electricity':
        setElectricityData({
          meterNumber: '',
          meterType: '',
          location: '',
          amount: '',
          phoneNumber: '',
          email: ''
        })
        break
      case 'Airtime':
        setAirtimeData({
          phoneNumber: '',
          network: '',
          amount: '',
          email: ''
        })
        break
      case 'Cable TV':
        setCableTVData({
          decoderNumber: '',
          provider: '',
          package: '',
          phoneNumber: '',
          email: ''
        })
        break
      case 'Internet':
        setInternetData({
          accountNumber: '',
          provider: '',
          package: '',
          phoneNumber: '',
          email: ''
        })
        break
      default:
        setGenericData({
          serviceId: '',
          provider: '',
          amount: '',
          phoneNumber: '',
          email: ''
        })
    }
  }

  const value = {
    activeService,
    setActiveService,
    electricityData,
    setElectricityData,
    airtimeData,
    setAirtimeData,
    cableTVData,
    setCableTVData,
    internetData,
    setInternetData,
    genericData,
    setGenericData,
    getCurrentFormData,
    resetCurrentFormData
  }

  return (
    <PayBillsContext.Provider value={value}>
      {children}
    </PayBillsContext.Provider>
  )
}

export function usePayBills() {
  const context = useContext(PayBillsContext)
  if (context === undefined) {
    throw new Error('usePayBills must be used within a PayBillsProvider')
  }
  return context
}
