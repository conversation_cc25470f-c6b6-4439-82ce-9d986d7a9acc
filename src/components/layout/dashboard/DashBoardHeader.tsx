"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/utils/classNames'
import { useUserDetails } from '@/app/(auth)/(onboarding)/misc/api'
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/core/Dialog'
import { Button } from '@/components/core/Button'

const navigationItems = [
  { name: 'Dashboard', href: '/dashboard', active: true },
  { name: 'Pay Bills', href: '/pay-bills', active: false },
  { name: 'Notifications', href: '/notifications', active: false },
  { name: 'FAQ', href: '/faq', active: false },
]

export default function DashBoardHeader() {
  const pathname = usePathname()

  const { data: userDetailsResponse } = useUserDetails({ retryCount: false })

  console.log(userDetailsResponse)

  return (
    <header className="">
      <div className="">
        <div className="flex items-center justify-between h-16">
          <div className='flex items-center gap-[29px]'>
            {/* Logo */}
            <div className="flex items-center">
              <svg width="144" height="48" viewBox="0 0 144 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect y="20.0244" width="8.00999" height="8.00999" rx="4.00499" fill="#105230" />
                <path d="M19.4293 31.5293L13.9398 16.5773H16.6953L20.9886 28.902L25.3461 16.5773H28.0588L22.5479 31.5293H19.4293ZM32.2947 31.5293V16.5773H41.9494V18.6492H34.8579V22.9426H41.3086V24.9718H34.8579V29.4574H41.9494V31.5293H32.2947ZM46.993 31.5293V16.5773H49.5562L56.7545 27.3855V16.5773H59.3177V31.5293H56.7545L49.5562 20.7425V31.5293H46.993ZM64.6743 31.5293V16.5773H69.5016C71.2389 16.5773 72.67 16.8835 73.795 17.4958C74.9342 18.1081 75.7743 18.9768 76.3154 20.1017C76.8708 21.2124 77.1485 22.5368 77.1485 24.0747C77.1485 25.5841 76.8708 26.9013 76.3154 28.0263C75.7743 29.137 74.9342 29.9985 73.795 30.6108C72.67 31.2231 71.2389 31.5293 69.5016 31.5293H64.6743ZM67.2375 29.3506H69.3734C70.6693 29.3506 71.6874 29.1441 72.4279 28.7311C73.1826 28.3039 73.7166 27.6987 74.0299 26.9155C74.3574 26.1181 74.5212 25.1711 74.5212 24.0747C74.5212 22.964 74.3574 22.017 74.0299 21.2338C73.7166 20.4364 73.1826 19.824 72.4279 19.3968C71.6874 18.9554 70.6693 18.7347 69.3734 18.7347H67.2375V29.3506ZM82.0009 31.5293V16.5773H87.8535C88.9073 16.5773 89.783 16.7411 90.4808 17.0686C91.1928 17.3961 91.7268 17.8518 92.0828 18.4356C92.4388 19.0052 92.6168 19.6674 92.6168 20.4221C92.6168 21.1768 92.453 21.8105 92.1255 22.3232C91.798 22.8358 91.3636 23.2274 90.8225 23.4979C90.2957 23.7685 89.7189 23.9323 89.0924 23.9892L89.4128 23.7543C90.0821 23.7685 90.6801 23.9465 91.207 24.2883C91.7481 24.63 92.1753 25.0786 92.4886 25.6339C92.8019 26.1751 92.9585 26.7803 92.9585 27.4495C92.9585 28.2327 92.7663 28.9376 92.3818 29.5642C92.0116 30.1765 91.4633 30.6607 90.7371 31.0167C90.0109 31.3584 89.1209 31.5293 88.0671 31.5293H82.0009ZM84.5641 29.436H87.6826C88.5228 29.436 89.1778 29.2367 89.6477 28.8379C90.1177 28.4392 90.3526 27.8839 90.3526 27.1719C90.3526 26.4599 90.1105 25.8974 89.6264 25.4844C89.1422 25.0572 88.4801 24.8436 87.6399 24.8436H84.5641V29.436ZM84.5641 22.8999H87.4904C88.3163 22.8999 88.9429 22.7148 89.3701 22.3445C89.7973 21.96 90.0109 21.4332 90.0109 20.7639C90.0109 20.1088 89.7973 19.5962 89.3701 19.226C88.9429 18.8415 88.3092 18.6492 87.469 18.6492H84.5641V22.8999ZM104.577 31.7856C103.139 31.7856 101.864 31.4652 100.753 30.8244C99.6569 30.1694 98.7954 29.2651 98.1689 28.1117C97.5423 26.944 97.229 25.5912 97.229 24.0533C97.229 22.5296 97.5423 21.1911 98.1689 20.0376C98.7954 18.87 99.6569 17.9586 100.753 17.3036C101.864 16.6485 103.139 16.321 104.577 16.321C106.044 16.321 107.332 16.6485 108.443 17.3036C109.554 17.9586 110.415 18.87 111.028 20.0376C111.64 21.1911 111.946 22.5296 111.946 24.0533C111.946 25.5912 111.64 26.944 111.028 28.1117C110.415 29.2651 109.554 30.1694 108.443 30.8244C107.332 31.4652 106.044 31.7856 104.577 31.7856ZM104.598 29.4787C105.552 29.4787 106.385 29.2651 107.097 28.8379C107.809 28.3965 108.358 27.7699 108.742 26.9583C109.127 26.1466 109.319 25.1783 109.319 24.0533C109.319 22.9283 109.127 21.96 108.742 21.1484C108.358 20.3367 107.809 19.7172 107.097 19.29C106.385 18.8486 105.552 18.6279 104.598 18.6279C103.644 18.6279 102.811 18.8486 102.099 19.29C101.387 19.7172 100.832 20.3367 100.433 21.1484C100.049 21.96 99.8563 22.9283 99.8563 24.0533C99.8563 25.1783 100.049 26.1466 100.433 26.9583C100.832 27.7699 101.387 28.3965 102.099 28.8379C102.811 29.2651 103.644 29.4787 104.598 29.4787ZM121.82 31.7856C120.737 31.7856 119.776 31.5934 118.936 31.2089C118.096 30.8244 117.441 30.2762 116.971 29.5642C116.501 28.8522 116.259 28.0049 116.245 27.0223H118.957C118.957 27.5207 119.071 27.9693 119.299 28.368C119.541 28.7525 119.869 29.0587 120.282 29.2865C120.709 29.5143 121.222 29.6283 121.82 29.6283C122.332 29.6283 122.774 29.5499 123.144 29.3933C123.528 29.2224 123.82 28.9875 124.02 28.6884C124.233 28.3751 124.34 28.012 124.34 27.5991C124.34 27.1291 124.226 26.7447 123.998 26.4456C123.785 26.1323 123.486 25.8689 123.101 25.6553C122.717 25.4417 122.275 25.2566 121.777 25.0999C121.279 24.9291 120.752 24.7511 120.196 24.5659C119.014 24.1672 118.124 23.6617 117.526 23.0494C116.928 22.4228 116.629 21.5898 116.629 20.5503C116.629 19.6816 116.836 18.934 117.249 18.3075C117.662 17.6809 118.238 17.1968 118.979 16.855C119.734 16.499 120.602 16.321 121.585 16.321C122.582 16.321 123.45 16.499 124.191 16.855C124.945 17.211 125.536 17.7094 125.964 18.3502C126.405 18.9768 126.633 19.7315 126.647 20.6144H123.913C123.899 20.2441 123.799 19.9024 123.614 19.5891C123.429 19.2616 123.158 18.9981 122.802 18.7988C122.46 18.5852 122.04 18.4784 121.542 18.4784C121.115 18.4641 120.73 18.5353 120.389 18.692C120.061 18.8344 119.798 19.048 119.598 19.3328C119.413 19.6033 119.321 19.9451 119.321 20.358C119.321 20.7568 119.406 21.0914 119.577 21.362C119.762 21.6183 120.025 21.839 120.367 22.0241C120.709 22.195 121.108 22.3588 121.563 22.5154C122.019 22.672 122.517 22.8429 123.059 23.028C123.799 23.2701 124.468 23.5691 125.066 23.9251C125.679 24.2669 126.163 24.7155 126.519 25.2708C126.875 25.8262 127.053 26.5453 127.053 27.4282C127.053 28.1971 126.854 28.9163 126.455 29.5855C126.056 30.2406 125.472 30.7746 124.703 31.1875C123.934 31.5863 122.973 31.7856 121.82 31.7856ZM136.914 31.7856C135.832 31.7856 134.871 31.5934 134.031 31.2089C133.191 30.8244 132.536 30.2762 132.066 29.5642C131.596 28.8522 131.354 28.0049 131.339 27.0223H134.052C134.052 27.5207 134.166 27.9693 134.394 28.368C134.636 28.7525 134.963 29.0587 135.376 29.2865C135.804 29.5143 136.316 29.6283 136.914 29.6283C137.427 29.6283 137.868 29.5499 138.239 29.3933C138.623 29.2224 138.915 28.9875 139.114 28.6884C139.328 28.3751 139.435 28.012 139.435 27.5991C139.435 27.1291 139.321 26.7447 139.093 26.4456C138.879 26.1323 138.58 25.8689 138.196 25.6553C137.811 25.4417 137.37 25.2566 136.872 25.0999C136.373 24.9291 135.846 24.7511 135.291 24.5659C134.109 24.1672 133.219 23.6617 132.621 23.0494C132.023 22.4228 131.724 21.5898 131.724 20.5503C131.724 19.6816 131.93 18.934 132.343 18.3075C132.756 17.6809 133.333 17.1968 134.073 16.855C134.828 16.499 135.697 16.321 136.679 16.321C137.676 16.321 138.545 16.499 139.285 16.855C140.04 17.211 140.631 17.7094 141.058 18.3502C141.5 18.9768 141.727 19.7315 141.742 20.6144H139.008C138.993 20.2441 138.894 19.9024 138.709 19.5891C138.523 19.2616 138.253 18.9981 137.897 18.7988C137.555 18.5852 137.135 18.4784 136.637 18.4784C136.209 18.4641 135.825 18.5353 135.483 18.692C135.156 18.8344 134.892 19.048 134.693 19.3328C134.508 19.6033 134.415 19.9451 134.415 20.358C134.415 20.7568 134.501 21.0914 134.672 21.362C134.857 21.6183 135.12 21.839 135.462 22.0241C135.804 22.195 136.202 22.3588 136.658 22.5154C137.114 22.672 137.612 22.8429 138.153 23.028C138.894 23.2701 139.563 23.5691 140.161 23.9251C140.773 24.2669 141.258 24.7155 141.614 25.2708C141.97 25.8262 142.148 26.5453 142.148 27.4282C142.148 28.1971 141.948 28.9163 141.549 29.5855C141.151 30.2406 140.567 30.7746 139.798 31.1875C139.029 31.5863 138.068 31.7856 136.914 31.7856Z" fill="#105230" />
              </svg>

            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'px-3 py-2 text-sm font-medium transition-colors duration-200',
                    pathname === item.href || (pathname === '/' && item.href === '/dashboard')
                      ? 'text-gray-900 border-b-2 border-gray-900'
                      : 'text-gray-500 hover:text-gray-700'
                  )}
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Mobile Menu */}
            <div className="md:hidden">
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outlined"
                    size="icon"
                    className="p-2 border-gray-300 hover:bg-gray-50"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </Button>
                </DialogTrigger>
                <DialogContent className="w-full max-w-sm mx-auto bg-white rounded-lg shadow-lg">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
                      <DialogClose className="p-1 rounded-full hover:bg-gray-100">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </DialogClose>
                    </div>

                    <nav className="space-y-4">
                      {navigationItems.map((item) => (
                        <DialogClose key={item.name} asChild>
                          <Link
                            href={item.href}
                            className={cn(
                              'block px-4 py-3 text-base font-medium rounded-lg transition-colors duration-200',
                              pathname === item.href || (pathname === '/' && item.href === '/dashboard')
                                ? 'bg-gray-100 text-gray-900 border-l-4 border-gray-900'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                            )}
                          >
                            {item.name}
                          </Link>
                        </DialogClose>
                      ))}
                    </nav>

                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        Welcome, <span className="font-semibold text-gray-900">{userDetailsResponse?.vas_store_data.store_name}</span>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-6">


            <div
              className="text-sm font-medium
               text-black hover:text-gray-700 transition-colors duration-200"
            >
              Welcome, <span className='font-bold text-base'>{userDetailsResponse?.vas_store_data.store_name}</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
