import React from 'react'
import Image from 'next/image' 

export default function Benefits() {
  const benefits = [
    {
      id: 1,
      image: '/images/Bulk.png',
      topic: 'No Bulk Payment',
      subtext: 'We pay the bulk of your rent upfront so you can move in without financial stress.',
    },
    {
      id: 2,
      image: '/images/Flexible.png',
      topic: 'Flexible Repayment Plans',
      subtext: 'Repay us in monthly installments that work with your income.',
    },
    {
      id: 3,
      image: '/images/Approval.png',
      topic: 'Fast Approval',
      subtext: 'Get approved within 24–48 hours. No lengthy paperwork.',
    },
    {
      id: 4,
      image: '/images/Secure.png',
      topic: 'Safe & Secure Platform',
      subtext: 'All your data and payments are protected with bank-level security.',
    },
    {
      id: 5,
      image: '/images/OneEye.png',
      topic: 'Transparent Process',
      subtext: 'No hidden fees, no surprises. Everything is clear from day one.',
    },
    {
      id: 6,
      image: '/images/Support.png',
      topic: '24/7 Customer Support',
      subtext: 'Our customer care team is always ready to assist, from application to move-in and beyond.',
    },
  ];

  return (
    <div className="bg-[#000619] py-[4.5rem] px-[6.25rem]">
      <div className="flex items-center justify-center mb-10">
        <div className="rounded-[0.5rem] bg-[#0E1325] w-[262px] flex items-center justify-center">
          <p className="text-white font-semibold text-[1.25rem] py-[0.8125rem]">Your Benefits</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[1.5rem]">
        {benefits.map((benefit) => (
          <div key={benefit.id} className="bg-[#0E1325] px-[2.625rem] pt-[2.625rem] pb-[4.0625rem] rounded-lg text-white">
        
            <Image 
            src={benefit.image} 
            alt={benefit.topic} 
            width={50}
            height={50}
            className="w-12 h-12 mb-4" />

            <h3 className="text-lg font-bold mb-2">{benefit.topic}</h3>
            <p className="text-sm">{benefit.subtext}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
