import ListCheck from '@/components/icons/HomePageIcons/Check'
import Image from 'next/image';
import Document from '@/components/icons/HomePageIcons/Document'
import React from 'react'

export default function Plan() {

    const planList = [
        { id: 1, image: <ListCheck />, planText: '3% per month' },
        { id: 2, image: <ListCheck />, planText: 'Two Guarantors ' },
        { id: 3, image: <ListCheck />, planText: '5% Processing Fee ' },
        { id: 4, image: <ListCheck />, planText: 'Bank Statement (Last 9months)' },
        { id: 5, image: <ListCheck />, planText: '50% Equity (refundable Commitment)' },
        { id: 6, image: <ListCheck />, planText: '⁠Employment Letter (for employed individuals)' }
    ]

    return (
        <div className="grid sm:grid-cols-2 w-full gap-[1.5rem]">
  <div className="bg-[#D6E0FF] py-[2.8125rem] px-[2rem]">
            <div className="">
 <div className="flex justify-between ">
                
                {/* Left Content */}
                <div>
                    <div className="flex gap-[0.75rem]">
                        <Document />
                        <p className='text-[#000619] font-semibold text-[1.25rem]'>
                            Option A - 12 Month Plan
                        </p>
                    </div>

                    <div>
                        {planList.map((plan) => (
                            <div className="mt-[1.5rem]" key={plan.id}>
                                <div className="flex gap-[0.75rem] mt-[1rem]">
                                    {plan.image}
                                    <p className='text-sm font-medium'>{plan.planText}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Image */}
                <div>
                    <Image
                        src="/images/BgHome.png"
                        width={358.33}
                        height={327.63}
                        alt="BgHome"
                        className=""
                    />
                </div>
            </div>

            
            </div>
           
        </div>



          <div className="bg-[#D6E0FF] py-[2.8125rem] px-[2rem]">
            <div className="">
 <div className="flex justify-between items-start">
                
                {/* Left Content */}
                <div>
                    <div className="flex gap-[0.75rem]">
                        <Document />
                        <p className='text-[#000619] font-semibold text-[1.25rem]'>
                            Option A - 12 Month Plan
                        </p>
                    </div>

                    <div>
                        {planList.map((plan) => (
                            <div className="mt-[1.5rem]" key={plan.id}>
                                <div className="flex gap-[0.75rem] mt-[1rem]">
                                    {plan.image}
                                    {plan.planText}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Image */}
                <div>
                    <Image
                        src="/images/BgHome.png"
                        width={358.33}
                        height={327.63}
                        alt="BgHome"
                        className=""
                    />
                </div>
            </div>

            
            </div>
           
        </div>
        </div>
      
    )
}
