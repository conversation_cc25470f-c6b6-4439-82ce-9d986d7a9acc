"use client"
import { But<PERSON> } from '@/components/core/Button';
import * as React from 'react';
import DstvIcon from './DstvIcon';
import StartEarningModal from './modals/StartEarningModal';
import LoginModal from './modals/LoginMerchantModal';
import { useBooleanStateControl } from '@/hooks';
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/core/Dialog';

// import StartEarningModal from './StartEarningModalNew';

export default function VendHero() {
  const {
    state: isLoginModalOpen,
    setState: setLoginModalState,
    setTrue: openLoginModal,
  } = useBooleanStateControl();

  const [isVisible, setIsVisible] = React.useState(false)
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentImage, setCurrentImage] = React.useState(0) // 0 for hero-man.svg, 1 for whatsapp-hero.svg
  const [isTransitioning, setIsTransitioning] = React.useState(false)
  const servicesScrollRef = React.useRef<HTMLDivElement>(null)

  const scrollLeft = () => {
    const container = servicesScrollRef.current
    if (!container) return
    const amount = Math.max(280, Math.round(container.clientWidth * 0.8))
    container.scrollBy({ left: -amount, behavior: 'smooth' })
  }

  const scrollRight = () => {
    const container = servicesScrollRef.current
    if (!container) return
    const amount = Math.max(280, Math.round(container.clientWidth * 0.8))
    container.scrollBy({ left: amount, behavior: 'smooth' })
  }

  React.useEffect(() => {
    setIsVisible(true)

    // Auto-switch images every 5 seconds
    const interval = setInterval(() => {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentImage(prev => prev === 0 ? 1 : 0)
        setIsTransitioning(false)
      }, 7000) // 7 second transition
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const images = [
    "/images/hero-man.svg",
    "/images/whatsapp-hero.svg"
  ]

  const services = [
    {
      name: 'Electricity', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      )
    },
    {
      name: 'Airtime', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      )
    },
    {
      name: 'Cable TV', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2" />
          <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" strokeWidth="2" />
          <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" strokeWidth="2" />
        </svg>
      )
    },
    {
      name: 'Internet', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
          <line x1="2" y1="12" x2="22" y2="12" stroke="currentColor" strokeWidth="2" />
          <path d="m12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="2" />
        </svg>
      )
    },
    {
      name: 'Education', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="m22 10-10-5-10 5 10 5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="m6 12 10 5 10-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="m6 16 10 5 10-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      )
    },
    {
      name: 'Waste Bill', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="2" />
          <path d="m19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" strokeWidth="2" />
          <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" strokeWidth="2" />
          <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" strokeWidth="2" />
        </svg>
      )
    },
    {
      name: 'Betting', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" />
          <circle cx="9" cy="9" r="2" fill="currentColor" />
          <path d="m21 15-6-6-6 6" stroke="currentColor" strokeWidth="2" />
          <path d="m9 21 3-3 3 3" stroke="currentColor" strokeWidth="2" />
        </svg>
      )
    },
    {
      name: 'Water Bill', icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z" stroke="currentColor" strokeWidth="2" />
        </svg>
      )
    },
    {
      name: 'Health Insurance', icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 2v2" /><path d="M5 2v2" /><path d="M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1" /><path d="M8 15a6 6 0 0 0 12 0v-3" /><circle cx="20" cy="10" r="2" /></svg>
      )
    },
  ]

  return (
    <>
      <style jsx>{`
        .no-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }

        @media (max-width: 1024px) {
          .hero-image-container {
            transform: none !important;
          }
        }

        /* Smooth scrolling for service icons */
        .service-scroll {
          scroll-behavior: smooth;
          -webkit-overflow-scrolling: touch;
        }

        /* Better touch targets for mobile */
        @media (max-width: 768px) {
          .touch-target {
            min-height: 44px;
            min-width: 44px;
          }
        }
      `}</style>

      <main className='bg-[#105230]'>
        <div className="relative bg-cover bg-center bg-no-repeat overflow-hidden" style={{ backgroundImage: 'url(/images/hero-section-img.svg)' }}>
          {/* Header Navigation */}
          <div className="relative z-10 container mx-auto px-4 sm:px-6 py-4 sm:py-6">
            <div className="flex justify-between items-center">
              {/* Logo - Responsive sizing */}
              <div className="flex items-center space-x-2">
                <svg
                  className="w-20 h-7 sm:w-24 sm:h-8 md:w-32 md:h-10"
                  viewBox="0 0 129 42"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect y="17.417" width="7.16667" height="7.16667" rx="3.58333" fill="white" />
                  <path d="M17.3898 27L12.4974 13.6222H15.1156L18.8996 24.4964L22.7027 13.6222H25.2827L20.3902 27H17.3898ZM28.9753 27V13.6222H37.7091V15.5907H31.4215V19.26H37.1358V21.1711H31.4215V25.0316H37.7091V27H28.9753ZM42.1261 27V13.6222H44.5724L50.8599 23.044V13.6222H53.3061V27H50.8599L44.5724 17.5973V27H42.1261ZM58.0018 27V13.6222H62.5694C64.1365 13.6222 65.4233 13.9025 66.4298 14.4631C67.4491 15.011 68.2008 15.7881 68.6849 16.7947C69.1818 17.7884 69.4303 18.9606 69.4303 20.3111C69.4303 21.6616 69.1818 22.8401 68.6849 23.8467C68.2008 24.8404 67.4491 25.6176 66.4298 26.1782C65.4233 26.7261 64.1365 27 62.5694 27H58.0018ZM60.448 24.8978H62.4547C63.5759 24.8978 64.4614 24.7194 65.1111 24.3627C65.7609 23.9932 66.226 23.4708 66.5063 22.7956C66.7865 22.1076 66.9267 21.2794 66.9267 20.3111C66.9267 19.3556 66.7865 18.5338 66.5063 17.8458C66.226 17.1578 65.7609 16.629 65.1111 16.2596C64.4614 15.8901 63.5759 15.7053 62.4547 15.7053H60.448V24.8978Z" fill="#EABB27" />
                  <path d="M73.6908 27V13.6222H79.0611C80.4498 13.6222 81.5073 13.9471 82.2335 14.5969C82.9725 15.2339 83.3419 16.0557 83.3419 17.0622C83.3419 17.9031 83.1126 18.5784 82.6539 19.088C82.208 19.5849 81.6602 19.9225 81.0104 20.1009C81.7748 20.2538 82.4055 20.636 82.9024 21.2476C83.3993 21.8464 83.6477 22.5471 83.6477 23.3498C83.6477 24.4073 83.2655 25.28 82.5011 25.968C81.7366 26.656 80.6536 27 79.2522 27H73.6908ZM76.1371 19.2409H78.6979C79.3859 19.2409 79.9147 19.0816 80.2842 18.7631C80.6536 18.4446 80.8384 17.9923 80.8384 17.4062C80.8384 16.8456 80.6536 16.4061 80.2842 16.0876C79.9274 15.7563 79.3859 15.5907 78.6597 15.5907H76.1371V19.2409ZM76.1371 25.0124H78.8699C79.5962 25.0124 80.1568 24.8468 80.5517 24.5156C80.9594 24.1716 81.1633 23.6938 81.1633 23.0822C81.1633 22.4579 80.9531 21.9674 80.5326 21.6107C80.1122 21.2539 79.5452 21.0756 78.8317 21.0756H76.1371V25.0124ZM94.1179 27.2293C92.7801 27.2293 91.608 26.9363 90.6015 26.3502C89.6077 25.7641 88.8241 24.9551 88.2508 23.9231C87.6902 22.8784 87.4099 21.6744 87.4099 20.3111C87.4099 18.9479 87.6902 17.7502 88.2508 16.7182C88.8241 15.6735 89.6077 14.8581 90.6015 14.272C91.608 13.6859 92.7801 13.3929 94.1179 13.3929C95.443 13.3929 96.6087 13.6859 97.6153 14.272C98.6218 14.8581 99.4053 15.6735 99.9659 16.7182C100.527 17.7502 100.807 18.9479 100.807 20.3111C100.807 21.6744 100.527 22.8784 99.9659 23.9231C99.4053 24.9551 98.6218 25.7641 97.6153 26.3502C96.6087 26.9363 95.443 27.2293 94.1179 27.2293ZM94.1179 25.0316C95.392 25.0316 96.4049 24.6111 97.1566 23.7702C97.921 22.9293 98.3033 21.7763 98.3033 20.3111C98.3033 18.8459 97.921 17.6929 97.1566 16.852C96.4049 16.0111 95.392 15.5907 94.1179 15.5907C92.8439 15.5907 91.8246 16.0111 91.0601 16.852C90.2957 17.6929 89.9135 18.8459 89.9135 20.3111C89.9135 21.7763 90.2957 22.9293 91.0601 23.7702C91.8246 24.6111 92.8439 25.0316 94.1179 25.0316ZM109.636 27.2293C108.655 27.2293 107.789 27.0637 107.037 26.7324C106.286 26.3884 105.693 25.9043 105.26 25.28C104.827 24.643 104.604 23.8721 104.591 22.9676H107.171C107.197 23.5919 107.42 24.1206 107.84 24.5538C108.273 24.9742 108.866 25.1844 109.617 25.1844C110.267 25.1844 110.783 25.0316 111.165 24.7258C111.548 24.4073 111.739 23.9868 111.739 23.4644C111.739 22.9166 111.567 22.4898 111.223 22.184C110.891 21.8782 110.445 21.6298 109.885 21.4387C109.324 21.2476 108.725 21.0437 108.088 20.8271C107.056 20.4704 106.267 20.0117 105.719 19.4511C105.184 18.8905 104.916 18.1452 104.916 17.2151C104.903 16.4252 105.088 15.7499 105.47 15.1893C105.865 14.616 106.4 14.1764 107.076 13.8707C107.751 13.5521 108.528 13.3929 109.407 13.3929C110.299 13.3929 111.083 13.5521 111.758 13.8707C112.446 14.1892 112.981 14.6351 113.363 15.2084C113.758 15.7818 113.968 16.4634 113.994 17.2533H111.376C111.363 16.7819 111.178 16.3679 110.821 16.0111C110.477 15.6416 109.993 15.4569 109.369 15.4569C108.834 15.4441 108.381 15.5779 108.012 15.8582C107.655 16.1258 107.477 16.5207 107.477 17.0431C107.477 17.489 107.617 17.8458 107.897 18.1133C108.178 18.3681 108.56 18.5847 109.044 18.7631C109.528 18.9415 110.082 19.1326 110.707 19.3364C111.369 19.5658 111.974 19.8333 112.522 20.1391C113.07 20.4449 113.51 20.8526 113.841 21.3622C114.172 21.8591 114.338 22.5025 114.338 23.2924C114.338 23.9932 114.159 24.643 113.803 25.2418C113.446 25.8406 112.917 26.3247 112.216 26.6942C111.516 27.051 110.656 27.2293 109.636 27.2293ZM123.142 27.2293C122.161 27.2293 121.294 27.0637 120.543 26.7324C119.791 26.3884 119.199 25.9043 118.765 25.28C118.332 24.643 118.109 23.8721 118.097 22.9676H120.677C120.702 23.5919 120.925 24.1206 121.345 24.5538C121.779 24.9742 122.371 25.1844 123.123 25.1844C123.773 25.1844 124.289 25.0316 124.671 24.7258C125.053 24.4073 125.244 23.9868 125.244 23.4644C125.244 22.9166 125.072 22.4898 124.728 22.184C124.397 21.8782 123.951 21.6298 123.39 21.4387C122.83 21.2476 122.231 21.0437 121.594 20.8271C120.562 20.4704 119.772 20.0117 119.224 19.4511C118.689 18.8905 118.421 18.1452 118.421 17.2151C118.409 16.4252 118.593 15.7499 118.976 15.1893C119.371 14.616 119.906 14.1764 120.581 13.8707C121.256 13.5521 122.033 13.3929 122.913 13.3929C123.804 13.3929 124.588 13.5521 125.263 13.8707C125.951 14.1892 126.486 14.6351 126.869 15.2084C127.264 15.7818 127.474 16.4634 127.499 17.2533H124.881C124.868 16.7819 124.684 16.3679 124.327 16.0111C123.983 15.6416 123.499 15.4569 122.874 15.4569C122.339 15.4441 121.887 15.5779 121.517 15.8582C121.161 16.1258 120.982 16.5207 120.982 17.0431C120.982 17.489 121.122 17.8458 121.403 18.1133C121.683 18.3681 122.065 18.5847 122.549 18.7631C123.034 18.9415 123.588 19.1326 124.212 19.3364C124.875 19.5658 125.48 19.8333 126.028 20.1391C126.576 20.4449 127.015 20.8526 127.346 21.3622C127.678 21.8591 127.843 22.5025 127.843 23.2924C127.843 23.9932 127.665 24.643 127.308 25.2418C126.951 25.8406 126.423 26.3247 125.722 26.6942C125.021 27.051 124.161 27.2293 123.142 27.2293Z" fill="white" />
                </svg>
              </div>

              {/* Desktop Navigation */}
              <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8 text-sm xl:text-base">
                <a href="#" className="text-[#F5F5F5] hover:text-yellow-400 transition-colors">Merchants</a>
                <a href="#" className="text-[#F5F5F5] hover:text-yellow-400 transition-colors">FAQ</a>
                <a href="#" className="text-[#F5F5F5] hover:text-yellow-400 transition-colors">Contact</a>
                <Button className="bg-[#ffffff4e] border-[0.4px] border-white text-white hover:bg-white hover:text-green-800 px-4 xl:px-6 py-2 rounded-3xl transition-colors text-sm"
                  onClick={
                    () => {
                      openLoginModal()
                    }
                  }>
                  Sign In
                  <svg className="w-4 h-4 xl:w-5 xl:h-5" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.4767 14.2809C15.3807 14.3636 15.2543 14.4109 15.1133 14.4004C14.8406 14.38 14.6318 14.1375 14.6522 13.8649L15.0408 8.65619L9.83212 8.26754C9.55946 8.2472 9.35065 8.00471 9.371 7.73206C9.39134 7.4594 9.63382 7.25059 9.90648 7.27093L15.6135 7.69677C15.8861 7.71711 16.0949 7.95959 16.0746 8.23225L15.6488 13.9392C15.6382 14.0803 15.5727 14.1983 15.4767 14.2809Z" fill="white" />
                    <path d="M15.8167 8.64818L7.31462 15.9696C7.10749 16.1479 6.78783 16.1241 6.60947 15.917C6.43111 15.7098 6.45496 15.3902 6.66208 15.2118L15.1642 7.89042C15.3713 7.71206 15.691 7.73591 15.8693 7.94303C16.0477 8.15016 16.0238 8.46982 15.8167 8.64818Z" fill="white" />
                  </svg>
                </Button>
              </nav>

              {/* Mobile Menu */}
              <div className="lg:hidden">
                <Dialog>
                  <DialogTrigger asChild>
                    <button className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors">
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                    </button>
                  </DialogTrigger>
                  <DialogContent className="w-full max-w-sm mx-auto bg-[#105230] border-[#105230] text-white rounded-lg shadow-xl">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-lg font-semibold text-white">Menu</h2>
                        <DialogClose className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors">
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </DialogClose>
                      </div>

                      <nav className="space-y-4">
                        <DialogClose asChild>
                          <a href="#" className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                            Merchants
                          </a>
                        </DialogClose>
                        <DialogClose asChild>
                          <a href="#" className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                            FAQ
                          </a>
                        </DialogClose>
                        <DialogClose asChild>
                          <a href="#" className="block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-colors border-b border-white/20">
                            Contact
                          </a>
                        </DialogClose>
                      </nav>

                      <div className="mt-6 pt-6 border-t border-white/20">
                        <DialogClose asChild>
                          <Button
                            className="w-full bg-[#ffffff4e] border-[0.4px] border-white text-white hover:bg-white hover:text-green-800 px-6 py-3 rounded-3xl transition-colors text-sm font-medium"
                            onClick={() => {
                              openLoginModal()
                            }}
                          >
                            Sign In
                            <svg className="w-4 h-4 ml-2" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M15.4767 14.2809C15.3807 14.3636 15.2543 14.4109 15.1133 14.4004C14.8406 14.38 14.6318 14.1375 14.6522 13.8649L15.0408 8.65619L9.83212 8.26754C9.55946 8.2472 9.35065 8.00471 9.371 7.73206C9.39134 7.4594 9.63382 7.25059 9.90648 7.27093L15.6135 7.69677C15.8861 7.71711 16.0949 7.95959 16.0746 8.23225L15.6488 13.9392C15.6382 14.0803 15.5727 14.1983 15.4767 14.2809Z" fill="currentColor" />
                              <path d="M15.8167 8.64818L7.31462 15.9696C7.10749 16.1479 6.78783 16.1241 6.60947 15.917C6.43111 15.7098 6.45496 15.3902 6.66208 15.2118L15.1642 7.89042C15.3713 7.71206 15.691 7.73591 15.8693 7.94303C16.0477 8.15016 16.0238 8.46982 15.8167 8.64818Z" fill="currentColor" />
                            </svg>
                          </Button>
                        </DialogClose>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>

          {/* Main content area - Responsive layout */}
          <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12 mt-4 sm:mt-6 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-6 items-start">
              {/* Left Column - WhatsApp Section */}
              <div className={`space-y-6 sm:space-y-8 text-center lg:text-left transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                <div className="space-y-4">
                  <p className="text-white text-base sm:text-lg font-semibold">Welcome to Vendboss store</p>
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight">
                    Turn your <span className="text-[#00EF75]">Whatsapp</span><br className="hidden sm:block" />
                    <span className="sm:hidden"> </span>into a vending machine
                  </h1>
                  <p className="text-white text-sm max-w-sm mx-auto lg:mx-0 leading-[22px]">
                    Sell airtime, data, TV subscriptions, electricity, and more — directly from your chat. No tech skills, no stress. Just share your link, and watch your wallet grow.
                  </p>
                </div>

                {/* WhatsApp Button */}
                <div className="flex justify-center lg:justify-start">
                  <Button className="bg-[#EABB27] hover:bg-yellow-500 text-black px-6 sm:px-8 py-3 sm:py-[10px] rounded-[60px] flex items-center gap-3 font-semibold text-sm transition-all duration-300 hover:scale-105">
                    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clipPath="url(#clip0_305_1188)">
                        <path d="M0.640234 14.8204C0.639531 17.3409 1.30328 19.8021 2.56539 21.9714L0.519531 29.3833L8.16391 27.3944C10.2782 28.5365 12.6472 29.135 15.0545 29.1351H15.0609C23.0079 29.1351 29.477 22.7185 29.4804 14.8316C29.482 11.0099 27.9834 7.41618 25.2606 4.71246C22.5384 2.00897 18.9179 0.519322 15.0603 0.517578C7.11227 0.517578 0.643633 6.93386 0.640352 14.8204" fill="url(#paint0_linear_305_1188)" />
                        <path d="M0.125391 14.8158C0.12457 17.4271 0.812109 19.9763 2.11922 22.2233L0 29.9008L7.91848 27.8407C10.1003 29.021 12.5568 29.6434 15.0564 29.6443H15.0628C23.295 29.6443 29.9965 22.9969 30 14.8277C30.0014 10.8686 28.4489 7.1457 25.6289 4.34512C22.8086 1.54488 19.0586 0.00162791 15.0628 0C6.82922 0 0.128672 6.64651 0.125391 14.8158ZM4.84113 21.8363L4.54547 21.3706C3.30258 19.4096 2.64656 17.1436 2.6475 14.8167C2.65008 8.02663 8.2193 2.50233 15.0675 2.50233C18.3839 2.50372 21.5006 3.78651 23.8448 6.11395C26.1889 8.44163 27.4788 11.5358 27.478 14.8267C27.475 21.6169 21.9056 27.1419 15.0628 27.1419H15.0579C12.8298 27.1407 10.6446 26.547 8.73891 25.425L8.28539 25.1581L3.58641 26.3806L4.84113 21.8363Z" fill="url(#paint1_linear_305_1188)" />
                        <path d="M11.3293 8.62213C11.0497 8.00551 10.7554 7.99306 10.4895 7.98225C10.2718 7.97295 10.0229 7.97364 9.77422 7.97364C9.52531 7.97364 9.1209 8.06655 8.77906 8.4369C8.43687 8.8076 7.47266 9.70341 7.47266 11.5254C7.47266 13.3474 8.81012 15.1083 8.99656 15.3556C9.18324 15.6025 11.5786 19.4611 15.3721 20.9455C18.525 22.1791 19.1666 21.9338 19.8508 21.8719C20.5352 21.8103 22.0591 20.9763 22.37 20.1115C22.6811 19.2469 22.6811 18.5057 22.5879 18.3509C22.4946 18.1966 22.2457 18.1039 21.8724 17.9188C21.4992 17.7336 19.6641 16.8376 19.3221 16.714C18.9799 16.5905 18.7311 16.5289 18.4822 16.8997C18.2333 17.2699 17.5186 18.1039 17.3007 18.3509C17.0831 18.5984 16.8652 18.6292 16.4921 18.444C16.1186 18.2582 14.9166 17.8676 13.4906 16.6061C12.3811 15.6245 11.632 14.4122 11.4143 14.0414C11.1965 13.6712 11.3909 13.4705 11.5781 13.286C11.7458 13.12 11.9514 12.8535 12.1382 12.6374C12.3243 12.4211 12.3864 12.2668 12.5109 12.0198C12.6355 11.7726 12.5731 11.5563 12.48 11.3711C12.3864 11.1859 11.6612 9.35434 11.3293 8.62213Z" fill="white" />
                      </g>
                      <defs>
                        <linearGradient id="paint0_linear_305_1188" x1="1448.56" y1="2887.09" x2="1448.56" y2="0.517578" gradientUnits="userSpaceOnUse">
                          <stop stopColor="#1FAF38" />
                          <stop offset="1" stopColor="#60D669" />
                        </linearGradient>
                        <linearGradient id="paint1_linear_305_1188" x1="1500" y1="2990.08" x2="1500" y2="0" gradientUnits="userSpaceOnUse">
                          <stop stopColor="#F9F9F9" />
                          <stop offset="1" stopColor="white" />
                        </linearGradient>
                        <clipPath id="clip0_305_1188">
                          <rect width="30" height="30" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>

                    Start on Whatsapp
                  </Button>
                </div>
              </div>

              {/* Center Column - Hero Image */}
              <div className="flex justify-center lg:justify-start lg:-ml-8 xl:-ml-16 order-last lg:order-none">
                <div className="relative">
                  <div className="relative w-[280px] h-[300px] sm:w-[350px] sm:h-[380px] md:w-[400px] md:h-[430px] lg:w-[420px] lg:h-[450px] xl:w-[486px] xl:h-[517px] overflow-hidden rounded-lg">
                    {/* Current Image */}
                    <img
                      src={images[currentImage]}
                      alt="Hero Image"
                      className={`absolute inset-0 w-full h-full object-cover transition-all duration-2000 ease-in-out ${isTransitioning
                        ? 'opacity-0 scale-105 blur-sm'
                        : 'opacity-100 scale-100 blur-0'
                        }`}
                    />

                    {/* Next Image (for cross-fade effect) */}
                    <img
                      src={images[currentImage === 0 ? 1 : 0]}
                      alt="Hero Image"
                      className={`absolute inset-0 w-full h-full object-cover transition-all duration-2000 ease-in-out ${isTransitioning
                        ? 'opacity-100 scale-100 blur-0'
                        : 'opacity-0 scale-95 blur-sm'
                        }`}
                    />

                    {/* Image indicator dots */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 hidden space-x-2 z-10">
                      <div className={`w-2 h-2 rounded-full transition-all duration-300 ${currentImage === 0
                        ? 'bg-white scale-125 shadow-lg'
                        : 'bg-white/50 scale-100'
                        }`} />
                      <div className={`w-2 h-2 rounded-full transition-all duration-300 ${currentImage === 1
                        ? 'bg-white scale-125 shadow-lg'
                        : 'bg-white/50 scale-100'
                        }`} />
                    </div>

                    {/* Transition pulse effect */}
                    {isTransitioning && (
                      <div className="absolute inset-0 bg-white/10 animate-pulse rounded-lg" />
                    )}
                  </div>

                  {/* Floating Success Notification - Top Right */}
                  <div className={`absolute top-4 sm:top-8 -left-4 sm:-left-8 bg-white rounded-lg py-2 sm:py-3 px-3 sm:px-4 shadow-xl max-w-[160px] sm:max-w-[200px] z-20 transition-all duration-2000 delay-2000 animate-bounce ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
                    <div className="flex items-start space-x-2">
                      <div className="w-5 h-5 sm:w-6 sm:h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10 3L4.5 8.5L2 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </div>
                      <div className="text-[9px] text-gray-800 leading-tight">
                        <span className="font-medium">Buy data, airtime for</span>
                        <br />
                        <span className="font-bold">₦52,983.17</span>
                        <span className="font-medium"> commission</span>
                      </div>
                    </div>
                  </div>

                  {/* DSTV Package Notification - Bottom Left */}
                  <div className={`absolute bottom-4 sm:bottom-8 -left-4 sm:-left-8 bg-white rounded-lg py-2 sm:py-3 px-3 sm:px-4 shadow-xl flex items-center space-x-2 sm:space-x-3 z-20 transition-all duration-3000 delay-1400 animate-bounce ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'}`}>
                    <div className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0">
                      <DstvIcon />
                    </div>
                    <div>
                      <div className="text-[9px] font-medium text-gray-800">DSTV Family</div>
                      <div className="text-xs font-bold text-gray-800">₦7,800</div>
                    </div>
                  </div>

                  {/* Floating Commission Notification - Bottom Right */}
                  <div className={`absolute bottom-0 -right-8 sm:-right-12 bg-yellow-400 rounded-lg py-2 px-3 shadow-xl max-w-[140px] sm:max-w-[180px] z-20 transition-all duration-4000 delay-1600 animate-bounce ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-4 opacity-0'}`}>
                    <div className="flex items-start gap-1">
                      <div >
                        <svg width="22" height="22" viewBox="0 0 22 22" className='w-5 h-5' fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10.885 20.1615C5.76721 20.1615 1.60742 16.0017 1.60742 10.884C1.60742 5.76623 5.76721 1.60645 10.885 1.60645C16.0027 1.60645 20.1625 5.76623 20.1625 10.884C20.1625 16.0017 16.0027 20.1615 10.885 20.1615ZM10.885 2.90099C6.48352 2.90099 2.90196 6.48255 2.90196 10.884C2.90196 15.2854 6.48352 18.867 10.885 18.867C15.2864 18.867 18.868 15.2854 18.868 10.884C18.868 6.48255 15.2864 2.90099 10.885 2.90099Z" fill="black" />
                          <path d="M9.65974 13.9748C9.48714 13.9748 9.32316 13.9058 9.20234 13.7849L6.75997 11.3426C6.5097 11.0923 6.5097 10.678 6.75997 10.4278C7.01025 10.1775 7.4245 10.1775 7.67478 10.4278L9.65974 12.4127L14.0957 7.97677C14.346 7.72649 14.7602 7.72649 15.0105 7.97677C15.2608 8.22705 15.2608 8.6413 15.0105 8.89158L10.1171 13.7849C9.99632 13.9058 9.83235 13.9748 9.65974 13.9748Z" fill="black" />
                        </svg>
                      </div>


                      <p className='text-[9px] text-black font-medium'>  You withdrew ₦52,983.17 commission to your wallet. Proceed</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Make Money Section */}
              <div className={`mt-8 sm:mt-16 lg:mt-48 space-y-6 sm:space-y-8 text-center lg:text-left transition-all duration-1000 delay-700 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                <div className="space-y-4">
                  <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight">
                    Make money while<br className="hidden sm:block" />
                    <span className="sm:hidden"> </span>you <span className="text-yellow-400">sleep.</span>
                  </h2>
                  <p className="text-white text-sm max-w-sm mx-auto lg:mx-0 leading-[22px]">
                    Get your own personalised vending website that works 24/7. Friends, family, and customers can buy anytime — even while you rest. Every sale credits instantly to your wallet with clear visibility from your backend dashboard.
                  </p>
                </div>

                {/* Start Now Button */}
                <div className="flex justify-center lg:justify-start mb-4 sm:m-0">
                  <Button
                    onClick={() => setIsModalOpen(true)}
                    className="text-[#EABB27] hover:text-yellow-300 font-semibold flex items-center text-sm sm:text-base transition-all duration-300 hover:scale-105 p-0 bg-transparent border-none">
                    Start Now
                    <svg className='ml-2 transition-transform duration-300 group-hover:translate-x-1' width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12.0253 15.6829C11.8669 15.6829 11.7086 15.6246 11.5836 15.4996C11.3419 15.2579 11.3419 14.8579 11.5836 14.6163L16.2003 9.99961L11.5836 5.38294C11.3419 5.14128 11.3419 4.74128 11.5836 4.49961C11.8253 4.25794 12.2253 4.25794 12.4669 4.49961L17.5253 9.55794C17.7669 9.79961 17.7669 10.1996 17.5253 10.4413L12.4669 15.4996C12.3419 15.6246 12.1836 15.6829 12.0253 15.6829Z" fill="currentColor" />
                      <path d="M16.942 10.625H2.91699C2.57533 10.625 2.29199 10.3417 2.29199 10C2.29199 9.65833 2.57533 9.375 2.91699 9.375H16.942C17.2837 9.375 17.567 9.65833 17.567 10C17.567 10.3417 17.2837 10.625 16.942 10.625Z" fill="currentColor" />
                    </svg>
                  </Button>
                </div>
              </div>

            </div>
          </div>
        </div>

        {/* Power Up Your Everyday Section */}
        <section className="relative z-20 -mt-8 sm:-mt-12 px-4 sm:px-0">
          <div className="">
            <div className="space-y-6 sm:space-y-8 bg-[#092D1A80] backdrop-blur-xl backdrop-saturate-150 border-[#FFFFFF4D] border-[0.5px] rounded-lg p-4 sm:p-6 relative z-30">

              {/* Mobile Layout */}
              <div className="block lg:hidden space-y-6">
                <div className="text-center">
                  <h3 className="text-lg sm:text-xl font-medium text-white mb-1">Power Up Your</h3>
                  <h3 className="text-lg sm:text-xl font-medium text-white mb-3">Everyday (Vending)</h3>
                  <p className="text-[#F2F2F2] text-sm max-w-[300px] mx-auto leading-relaxed">
                    Recharge airtime, data, and electricity in seconds. Pay for education, entertainment, and all your daily utilities — anytime, anywhere.
                  </p>
                </div>

                {/* Service Icons - Mobile */}
                <div ref={servicesScrollRef} className="flex flex-nowrap justify-start gap-3 overflow-x-auto pb-4 no-scrollbar service-scroll">
                  {services.map((service, index) => (
                    <div
                      key={service.name}
                      className={`flex flex-col items-center justify-center space-y-2 p-3 sm:p-4 rounded-xl bg-[#092D1A80] border border-white/30 min-w-[80px] sm:min-w-[90px] h-[80px] sm:h-[90px] hover:bg-white/5 transition-all duration-500 delay-${index * 100} ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
                    >
                      <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-white">
                        {service.icon}
                      </div>
                      <span className="text-white text-xs font-medium text-center whitespace-nowrap">{service.name}</span>
                    </div>
                  ))}
                </div>

                {/* Navigation Arrows - Mobile */}
                <div className="flex justify-center space-x-4">
                  <button className="p-2 hover:bg-white/10 rounded-full transition-colors" onClick={scrollLeft}>
                    <svg className="w-6 h-6 sm:w-8 sm:h-8" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="15" cy="15" r="15" fill="white" fillOpacity="0.04" />
                      <circle cx="15" cy="15" r="14.7" stroke="white" strokeOpacity="0.1" strokeWidth="0.6" />
                      <path d="M16.8984 20.3281C16.75 20.3281 16.6016 20.2734 16.4844 20.1562C16.2578 19.9297 16.2578 19.5547 16.4844 19.3281L20.8125 15L16.4844 10.6719C16.2578 10.4453 16.2578 10.0703 16.4844 9.84375C16.7109 9.61719 17.0859 9.61719 17.3125 9.84375L22.0547 14.5859C22.2812 14.8125 22.2812 15.1875 22.0547 15.4141L17.3125 20.1562C17.1953 20.2734 17.0469 20.3281 16.8984 20.3281Z" fill="white" />
                      <path d="M21.5078 15.5859H8.35938C8.03906 15.5859 7.77344 15.3203 7.77344 15C7.77344 14.6797 8.03906 14.4141 8.35938 14.4141H21.5078C21.8281 14.4141 22.0938 14.6797 22.0938 15C22.0938 15.3203 21.8281 15.5859 21.5078 15.5859Z" fill="white" />
                    </svg>
                  </button>
                  <button className="p-2 hover:bg-white/10 rounded-full transition-colors" onClick={scrollRight}>
                    <svg className="w-6 h-6 sm:w-8 sm:h-8" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="15" cy="15" r="15" fill="white" fillOpacity="0.04" />
                      <circle cx="15" cy="15" r="14.7" stroke="white" strokeOpacity="0.1" strokeWidth="0.6" />
                      <path d="M13.1016 9.67188C13.25 9.67188 13.3984 9.72656 13.5156 9.84375C13.7422 10.0703 13.7422 10.4453 13.5156 10.6719L9.1875 15L13.5156 19.3281C13.7422 19.5547 13.7422 19.9297 13.5156 20.1562C13.2891 20.3828 12.9141 20.3828 12.6875 20.1562L7.94531 15.4141C7.71875 15.1875 7.71875 14.8125 7.94531 14.5859L12.6875 9.84375C12.8047 9.72656 12.9531 9.67188 13.1016 9.67188Z" fill="white" />
                      <path d="M8.49219 14.4141H21.6406C21.9609 14.4141 22.2266 14.6797 22.2266 15C22.2266 15.3203 21.9609 15.5859 21.6406 15.5859H8.49219C8.17188 15.5859 7.90625 15.3203 7.90625 15C7.90625 14.6797 8.17188 14.4141 8.49219 14.4141Z" fill="white" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Desktop Layout */}
              <div className="hidden lg:flex items-start justify-between xl:ml-[96px]">
                <div>
                  <div className="text-left">
                    <h3 className="text-xl font-medium text-white mb-1">Power Up Your</h3>
                    <h3 className="text-xl font-medium text-white mb-2">Everyday (Vending)</h3>
                    <p className="text-[#F2F2F2] text-xs max-w-[286px]">
                      Recharge airtime, data, and electricity in seconds.<br />
                      Pay for education, entertainment, and all your daily<br />
                      utilities — anytime, anywhere.
                    </p>
                  </div>
                  {/* Navigation Arrows - Desktop */}
                  <div className="flex space-x-2 mt-4">
                    <button className="hover:bg-white/10 rounded-full transition-colors" onClick={scrollLeft}>
                      <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="15" fill="white" fillOpacity="0.04" />
                        <circle cx="15" cy="15" r="14.7" stroke="white" strokeOpacity="0.1" strokeWidth="0.6" />
                        <path d="M16.8984 20.3281C16.75 20.3281 16.6016 20.2734 16.4844 20.1562C16.2578 19.9297 16.2578 19.5547 16.4844 19.3281L20.8125 15L16.4844 10.6719C16.2578 10.4453 16.2578 10.0703 16.4844 9.84375C16.7109 9.61719 17.0859 9.61719 17.3125 9.84375L22.0547 14.5859C22.2812 14.8125 22.2812 15.1875 22.0547 15.4141L17.3125 20.1562C17.1953 20.2734 17.0469 20.3281 16.8984 20.3281Z" fill="white" />
                        <path d="M21.5078 15.5859H8.35938C8.03906 15.5859 7.77344 15.3203 7.77344 15C7.77344 14.6797 8.03906 14.4141 8.35938 14.4141H21.5078C21.8281 14.4141 22.0938 14.6797 22.0938 15C22.0938 15.3203 21.8281 15.5859 21.5078 15.5859Z" fill="white" />
                      </svg>
                    </button>
                    <button className="hover:bg-white/10 rounded-full transition-colors" onClick={scrollRight}>
                      <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="15" fill="white" fillOpacity="0.04" />
                        <circle cx="15" cy="15" r="14.7" stroke="white" strokeOpacity="0.1" strokeWidth="0.6" />
                        <path d="M13.1016 9.67188C13.25 9.67188 13.3984 9.72656 13.5156 9.84375C13.7422 10.0703 13.7422 10.4453 13.5156 10.6719L9.1875 15L13.5156 19.3281C13.7422 19.5547 13.7422 19.9297 13.5156 20.1562C13.2891 20.3828 12.9141 20.3828 12.6875 20.1562L7.94531 15.4141C7.71875 15.1875 7.71875 14.8125 7.94531 14.5859L12.6875 9.84375C12.8047 9.72656 12.9531 9.67188 13.1016 9.67188Z" fill="white" />
                        <path d="M8.49219 14.4141H21.6406C21.9609 14.4141 22.2266 14.6797 22.2266 15C22.2266 15.3203 21.9609 15.5859 21.6406 15.5859H8.49219C8.17188 15.5859 7.90625 15.3203 7.90625 15C7.90625 14.6797 8.17188 14.4141 8.49219 14.4141Z" fill="white" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Service Icons - Desktop */}
                <div className="flex items-center gap-6">
                  <div ref={servicesScrollRef} className="flex flex-nowrap justify-start gap-3 overflow-x-auto pb-4 max-w-[520px] lg:max-w-[1000px] no-scrollbar service-scroll">
                    {services.map((service, index) => (
                      <div
                        key={service.name}
                        className={`flex flex-col items-center justify-center space-y-2 p-4 rounded-xl bg-[#092D1A80] border border-white/30 min-w-[90px] h-[90px] hover:bg-white/5 transition-all duration-500 delay-${index * 100} ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
                      >
                        <div className="w-6 h-6 flex items-center justify-center text-white">
                          {service.icon}
                        </div>
                        <span className="text-white text-xs font-medium text-center whitespace-nowrap">{service.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

      </main>

      {/* Start Earning Modal */}
      <StartEarningModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />

      <LoginModal
        isLoginOpen={isLoginModalOpen}
        setLoginModalState={setLoginModalState}
      />
    </>
  );
}
