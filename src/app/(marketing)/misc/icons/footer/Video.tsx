import * as React from "react";
import { SVGProps } from "react";
const Video = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={20} cy={20} r={20} fill="#fff" fillOpacity={0.2} />
    <path
      d="M12.042 19.516c0-2.514.25-3.772 1.03-4.552.781-.781 2.039-.781 4.553-.781h4.75c2.514 0 3.77 0 4.552.78.782.781 1.031 2.039 1.031 4.553v.968c0 2.514-.25 3.772-1.03 4.553-.781.78-2.039.78-4.553.78h-4.75c-2.514 0-3.772 0-4.552-.78-.781-.781-1.031-2.038-1.031-4.553z"
      stroke="#fff"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="m22.238 19.576-3.556-2.223a.5.5 0 0 0-.765.424v4.446a.5.5 0 0 0 .765.424l3.556-2.223a.5.5 0 0 0 0-.848Z"
      stroke="#fff"
      strokeWidth={1.5}
      strokeLinejoin="round"
    />
  </svg>
);
export default Video;
