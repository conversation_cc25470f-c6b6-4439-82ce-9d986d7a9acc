import Link from 'next/link'
import React, { useState } from 'react'

import { cn } from '@/utils/classNames'
import { Button } from '../core/Button'


export const LandingHeader = () => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen)
    }

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false)
    }

    return (
        <>
            <header className='bg-[#080D27] relative z-40'>
                <section className='px-4 py-4'>
                    <div className='flex items-center justify-between'>
                        <div className="flex items-center gap-4 lg:gap-10 xl:gap-[5.625rem]">
                            <Link href="/" className="text-white font-bold text-xl">
                                VENDBOSS
                            </Link>
                        </div>
                        {/* Desktop Navigation */}
                        <ul className='hidden md:flex items-center justify-center space-x-8 text-sm text-white'>
                            <li>
                                <Link href='/' className="hover:text-white/80 transition-colors">Home</Link>
                            </li>
                            <li>
                                <Link href='/' className="hover:text-white/80 transition-colors">Plan</Link>
                            </li>
                            <li>
                                <Link href='/' className="hover:text-white/80 transition-colors">About us</Link>
                            </li>
                            <li>
                                <Link href='/' className="hover:text-white/80 transition-colors">FAQs</Link>
                            </li>
                            <li>
                                <Link href='/' className="hover:text-white/80 transition-colors">Contact us</Link>
                            </li>
                        </ul>

                        {/* Desktop CTA */}
                        <div className='hidden md:flex justify-end items-center space-x-6'>
                            <Link href='/' className="text-white hover:text-white/80 transition-colors text-sm">
                                Login
                            </Link>
                            <button className='flex justify-between items-center bg-white text-[#080D27] rounded-full text-sm py-3 pl-6 pr-3 gap-4 hover:bg-white/90 transition-colors font-medium'>
                                Get insurance
                                <svg width="24" height="24" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="15" fill="#032282" />
                                    <path d="M10.9168 19.6171C11.0334 19.6171 11.1501 19.5587 11.2084 19.5004L19.3751 11.3337C19.5501 11.1587 19.5501 10.9254 19.3751 10.7504C19.2001 10.5754 18.9084 10.5754 18.7334 10.7504L10.5668 18.9171C10.3918 19.0921 10.3918 19.3837 10.5668 19.5587C10.6834 19.6171 10.8001 19.6171 10.9168 19.6171Z" fill="white" />
                                    <path d="M19.0834 17.4585C19.3167 17.4585 19.55 17.2835 19.55 16.9919V11.0419C19.55 10.8085 19.375 10.5752 19.0834 10.5752H13.075C12.8417 10.5752 12.6084 10.7502 12.6084 11.0419C12.6084 11.3335 12.7834 11.5085 13.075 11.5085H18.6167V17.0502C18.6167 17.2835 18.85 17.4585 19.0834 17.4585Z" fill="white" />
                                </svg>
                            </button>
                        </div>
                        {/* Mobile Menu Button */}
                        <div className="md:hidden">
                            <button
                                onClick={toggleMobileMenu}
                                className="p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
                                aria-label="Toggle mobile menu"
                            >
                                <svg
                                    className={cn("w-6 h-6 transition-transform duration-300", isMobileMenuOpen && "rotate-90")}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    {isMobileMenuOpen ? (
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    ) : (
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    )}
                                </svg>
                            </button>
                        </div>
                    </div>
                </section>
            </header>

            {/* Mobile Slide-in Navigation */}
            <div className={cn(
                "fixed inset-0 z-50 md:hidden transition-all duration-300 ease-in-out",
                isMobileMenuOpen ? "visible" : "invisible"
            )}>
                {/* Backdrop */}
                <div
                    className={cn(
                        "absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300",
                        isMobileMenuOpen ? "opacity-100" : "opacity-0"
                    )}
                    onClick={closeMobileMenu}
                />

                {/* Slide-in Menu */}
                <div className={cn(
                    "absolute right-0 top-0 h-full w-80 max-w-[85vw] bg-gradient-to-br from-[#080D27] via-[#0A1235] to-[#0C1640] shadow-2xl transform transition-transform duration-300 ease-out",
                    isMobileMenuOpen ? "translate-x-0" : "translate-x-full"
                )}>
                    {/* Header */}
                    <div className="flex items-center justify-between p-6 border-b border-white/10">
                        <div className="text-white font-bold text-xl">VENDBOSS</div>
                        <button
                            onClick={closeMobileMenu}
                            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Navigation Links */}
                    <nav className="px-6 py-8">
                        <div className="space-y-2">
                            {[
                                { name: 'Home', href: '/', icon: '🏠' },
                                { name: 'Plan', href: '/', icon: '📋' },
                                { name: 'About us', href: '/', icon: '👥' },
                                { name: 'FAQs', href: '/', icon: '❓' },
                                { name: 'Contact us', href: '/', icon: '📞' }
                            ].map((item, index) => (
                                <Link
                                    key={item.name}
                                    href={item.href}
                                    onClick={closeMobileMenu}
                                    className={cn(
                                        "flex items-center space-x-4 px-4 py-4 text-white rounded-xl transition-all duration-200 hover:bg-white/10 hover:translate-x-2 group",
                                        "animate-in slide-in-from-right-5 fade-in"
                                    )}
                                    style={{ animationDelay: `${index * 100}ms` }}
                                >
                                    <span className="text-xl group-hover:scale-110 transition-transform">{item.icon}</span>
                                    <span className="font-medium text-lg">{item.name}</span>
                                    <svg className="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </Link>
                            ))}
                        </div>
                    </nav>

                    {/* Bottom Section */}
                    <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-white/10 bg-gradient-to-t from-black/20">
                        <div className="space-y-4">
                            <Link
                                href='/'
                                onClick={closeMobileMenu}
                                className="block text-center text-white/80 hover:text-white transition-colors py-2"
                            >
                                Login
                            </Link>
                            <button
                                onClick={closeMobileMenu}
                                className="w-full flex justify-center items-center bg-white text-[#080D27] rounded-full text-base py-4 px-6 font-semibold hover:bg-white/90 transition-all hover:scale-105 shadow-lg"
                            >
                                Get insurance
                                <svg className="ml-3 w-5 h-5" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="15" fill="#032282" />
                                    <path d="M10.9168 19.6171C11.0334 19.6171 11.1501 19.5587 11.2084 19.5004L19.3751 11.3337C19.5501 11.1587 19.5501 10.9254 19.3751 10.7504C19.2001 10.5754 18.9084 10.5754 18.7334 10.7504L10.5668 18.9171C10.3918 19.0921 10.3918 19.3837 10.5668 19.5587C10.6834 19.6171 10.8001 19.6171 10.9168 19.6171Z" fill="white" />
                                    <path d="M19.0834 17.4585C19.3167 17.4585 19.55 17.2835 19.55 16.9919V11.0419C19.55 10.8085 19.375 10.5752 19.0834 10.5752H13.075C12.8417 10.5752 12.6084 10.7502 12.6084 11.0419C12.6084 11.3335 12.7834 11.5085 13.075 11.5085H18.6167V17.0502C18.6167 17.2835 18.85 17.4585 19.0834 17.4585Z" fill="white" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}