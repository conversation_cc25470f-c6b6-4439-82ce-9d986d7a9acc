"use client"

import React from 'react'
import DashBoardHeader from '@/components/layout/dashboard/DashBoardHeader'
import { But<PERSON> } from '@/components/core/Button'
// import { Copy } from 'lucide-react'
import { useRouter } from 'next/navigation'
import ElectricityForm from '@/components/pay-bills/ElectricityForm'
import AirtimeForm from '@/components/pay-bills/AirtimeForm'
import CableTVForm from '@/components/pay-bills/CableTVForm'
import InternetForm from '@/components/pay-bills/InternetForm'
import GenericServiceForm from '@/components/pay-bills/GenericServiceForm'
import { usePayBills } from '@/contexts/PayBillsContext'

const serviceTypes = [
  'Electricity',
  'Airtime',
  'Cable TV',
  'Internet',
  'Education',
  'Waste Bill',
  'Betting',
  'Water Bill'
]

export default function PayBillsPage() {
  const [copied, setCopied] = React.useState(false)
  const referralLink = "https://www.vendboss.com/{user}"

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const router = useRouter()
  const {
    activeService,
    setActiveService,
    electricityData,
    setElectricityData,
    airtimeData,
    setAirtimeData,
    cableTVData,
    setCableTVData,
    internetData,
    setInternetData,
    genericData,
    setGenericData,
    resetCurrentFormData
  } = usePayBills()

  // Handle input changes for different services
  const handleElectricityChange = (field: string, value: string) => {
    setElectricityData(prev => ({ ...prev, [field]: value }))
  }

  const handleAirtimeChange = (field: string, value: string) => {
    setAirtimeData(prev => ({ ...prev, [field]: value }))
  }

  const handleCableTVChange = (field: string, value: string) => {
    setCableTVData(prev => ({ ...prev, [field]: value }))
  }

  const handleInternetChange = (field: string, value: string) => {
    setInternetData(prev => ({ ...prev, [field]: value }))
  }

  const handleGenericChange = (field: string, value: string) => {
    setGenericData(prev => ({ ...prev, [field]: value }))
  }

  const handleCopyReferralLink = () => {
    navigator.clipboard.writeText('https://www.vendboss.com/09090930394')
  }

  const handleProceed = () => {
    // Navigate to preview page
    router.push('/pay-bills/preview')
  }

  const handleCancel = () => {
    resetCurrentFormData()
  }

  return (
    <div className="min-h-screen bg-[#F9FAFB] px-4 sm:px-6 lg:px-[92px] py-[31px]">
      {/* Header */}
      <DashBoardHeader />

      {/* Main Content */}
      <main className="py-8">

        {/* User Greeting and Referral Link */}
        <div className="rounded-lg mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl text-black mb-2">Hello, <span className='font-semibold'>09090930394</span></h1>
              <p className="text-black text-[13px]" >What do you want to buy today?</p>
            </div>

            <div>
              <span className="text-[13px] text-black mb-2">Your referral link</span>
              {/* Referral Link Input */}
              <div className="flex items-center bg-[#E7EEEA] rounded-lg p-2 w-[318px]">
                <input
                  type="text"
                  value={referralLink}
                  readOnly
                  className="bg-transparent text-xs text-[#105230] outline-none w-full"
                />
                <Button
                  onClick={handleCopyLink}
                  className="ml-2 bg-[#105230] hover:bg-green-700 text-white px-4 py-2 rounded-md text-xs font-medium transition-colors duration-200"
                >
                  {copied ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Service Tabs */}
        <div className="rounded-lg">
          <div className="flex flex-wrap gap-2 mb-8 border-b">
            {serviceTypes.map((service) => (
              <button
                key={service}
                onClick={() => setActiveService(service)}
                className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${activeService === service
                  ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
                  : 'text-gray-500 hover:text-gray-700'
                  }`}
              >
                {service}
              </button>
            ))}
          </div>

          {/* Service Form */}
          {activeService === 'Electricity' && (
            <ElectricityForm
              formData={electricityData}
              onInputChange={handleElectricityChange}
              onProceed={handleProceed}
              onCancel={handleCancel}
            />
          )}

          {activeService === 'Airtime' && (
            <AirtimeForm
              formData={airtimeData}
              onInputChange={handleAirtimeChange}
              onProceed={handleProceed}
              onCancel={handleCancel}
            />
          )}

          {activeService === 'Cable TV' && (
            <CableTVForm
              formData={cableTVData}
              onInputChange={handleCableTVChange}
              onProceed={handleProceed}
              onCancel={handleCancel}
            />
          )}

          {activeService === 'Internet' && (
            <InternetForm
              formData={internetData}
              onInputChange={handleInternetChange}
              onProceed={handleProceed}
              onCancel={handleCancel}
            />
          )}

          {['Education', 'Waste Bill', 'Betting', 'Water Bill'].includes(activeService) && (
            <GenericServiceForm
              serviceType={activeService}
              formData={genericData}
              onInputChange={handleGenericChange}
              onProceed={handleProceed}
              onCancel={handleCancel}
            />
          )}
        </div>
      </main>
    </div>
  )
}
