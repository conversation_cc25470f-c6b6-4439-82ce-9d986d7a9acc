'use client';

import { Button } from '@/components/core/Button';
import AddIcon from '@/components/icons/HomePageIcons/AddIcon';
import DoubleRigtArrow from '@/components/icons/HomePageIcons/Doublerightarrow';
import MinusIcon from '@/components/icons/HomePageIcons/MinusIcon';
import React, { useState } from 'react';

export default function FAQ() {
    const [activeIndex, setActiveIndex] = useState<number | null>(null);

    const toggleAccordion = (index: number) => {
        setActiveIndex((prevIndex) => (prevIndex === index ? null : index));
    };

    const accordionData = [
        {
            title: 'How does Rent Now, Pay Later work?',
            content:
                'We pay your landlord upfront so you can move in immediately. You then repay us in monthly installments based on your agreed plan.',
        },
        {
            title: 'Who is eligible to use this service?',
            content:
                'Gain full control over your business spending with real-time expense tracking, requisition approvals, and seamless procurement workflows.',
        },
        {
            title: 'What documents do I need to apply?',
            content:
                'Stay on top of your inventory with real-time tracking and automated low-stock alerts, to ensure your shelves are always stocked.',
        },
        {
            title: 'What happens if I miss a payment?',
            content:
                'Boost your business with a centralised platform to manage sales, create custom invoices, and track transactions—whether through POS or online or offline.',
        },
        {
            title: 'Is there any interest or extra fees?',
            content:
                'Boost your business with a centralised platform to manage sales, create custom invoices, and track transactions—whether through POS or online or offline.',
        },
    ];

    return (
        <div className="bg-[#000619] py-[4.5rem] px-[6.25rem]">
            {/* Section Title */}

            {/* Grid Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-[3rem]">
                {/* Left: Text and Button */}
                <div className="">

                    <div className="mb-10">
                        <div className="rounded-[0.5rem] bg-[#0E1325] w-[262px]">
                            <p className="text-white font-semibold text-[1.25rem] py-[0.8125rem] flex items-center justify-center">
                                FAQs
                            </p>
                        </div>
                    </div>

                    <div className="max-w-[30rem]">
                        <p className="font-medium text-[3.75rem] text-[#FFF] leading-[1.1]">
                            Need Answers? We’ve Got You
                        </p>
                    </div>


                    <Button className="bg-[#2E6CE7] text-[#FFFFFF] font-medium text-[1rem] mt-[2.625rem] cursor-pointer">
                        Contact sales
                        <DoubleRigtArrow />
                    </Button>
                </div>

                {/* Right: Accordion */}
                <div className="flex flex-col gap-4">
                    {accordionData?.map((item, index) => (
                        <div
                            key={index}
                            className="w-full bg-[#FFFFFF0D]/5 border-[0.4px] border-[#B1BAD8] rounded-lg px-[0.8919rem] py-3"
                        >
                            {/* Trigger */}
                            <div className="flex items-center gap-2">
                                <span className="text-[#fff] font-semibold">{item.title}</span>
                                <Button
                                    onClick={() => toggleAccordion(index)}
                                    className="ml-auto p-1 bg-transparent focus:outline-none"
                                >
                                    {activeIndex === index ? <MinusIcon /> : <AddIcon />}
                                </Button>
                            </div>

                            {/* Content (only shown when active) */}
                            {activeIndex === index && (
                                <div className="mt-3 pl-1">
                                    <p className="text-[#FFFFFFB2]">{item.content}</p>
                                </div>
                            )}
                        </div>
                    ))}
                </div>

            </div>
        </div>
    );
}
