{"name": "my-rnpl-application", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@tanstack/react-query": "^4.40.1", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}