import * as React from "react";
import { SVGProps } from "react";
const Facebook = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={20} cy={20} r={20} fill="#fff" fillOpacity={0.2} />
    <g clipPath="url(#a)">
      <path
        d="M21.665 21.247h2.084l.833-3.333h-2.917v-1.667c0-.858 0-1.666 1.667-1.666h1.25v-2.8a24 24 0 0 0-2.38-.117c-2.263 0-3.87 1.38-3.87 3.917v2.333h-2.5v3.333h2.5v7.084h3.333z"
        fill="#fff"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M10 10h20v20H10z" />
      </clipPath>
    </defs>
  </svg>
);
export default Facebook;
