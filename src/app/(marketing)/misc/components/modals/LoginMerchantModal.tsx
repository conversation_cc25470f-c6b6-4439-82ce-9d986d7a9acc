"use client"

import React from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { But<PERSON> } from '@/components/core/Button'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogBody,
    DialogClose,
} from '@/components/core/Dialog'
import { ErrorModal, Input } from '@/components/core'

import { useLogin } from '../../api/login'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { AxiosError } from 'axios'
import useErrorModalState from '@/hooks/useErrorModalState'

// Zod validation schema for login
const loginSchema = z.object({
    phone: z.string().min(10, 'Phone number must be at least 10 digits').regex(/^\d+$/, 'Phone number must contain only digits'),
    password: z.string().min(6, "password must be at least 6 characters"),
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginModalProps {
    isLoginOpen: boolean;
    setLoginModalState: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function LoginModal({ isLoginOpen, setLoginModalState }: LoginModalProps) {
    const [showPassword, setShowPassword] = React.useState(false);
    const router = useRouter()


    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    const { mutate: postLogIn, isPending: isLoginLoading } = useLogin();

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm<LoginFormData>({
        resolver: zodResolver(loginSchema)
    })

    const onSubmit = async (data: LoginFormData) => {

        postLogIn(data, {
            onSuccess: () => {

                // setEmail(encryptedEmail);
                // setPassword(encryptedPassword);

                router.push('/dashboard');
            },
            onError: error => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                if (errorMessage === "You do not have any pin yet. Please create a transaction pin with link below") {
                    router.push('/transaction-pin');
                }
                openErrorModalWithMessage(errorMessage as string);
                // if (errorMessage === missingPinResponse) {
                //   router.push('/transaction-pin');
                // }

            },
        });
    }

    const handleCancel = () => {
        reset()

    }

    return (
        <Dialog open={isLoginOpen} onOpenChange={setLoginModalState}>
            <DialogContent
                className="w-full max-w-lg mx-4 bg-white rounded-2xl border-0 p-0 max-h-[90vh] overflow-y-auto no-scrollbar"
                overlayClassName="bg-black/60 backdrop-blur-sm"
            >
                <div className="p-6">
                    <DialogHeader className="text-right bg-transparent px-0 py-0">
                        <DialogTitle className='text-lg font-medium text-black'>
                            Welcome back
                        </DialogTitle>
                        <DialogClose >
                            <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="17" cy="17" r="17" fill="#F9FAFB" />
                                <line x1="12.8201" y1="12.1123" x2="21.0141" y2="20.3063" stroke="black" />
                                <line y1="-0.5" x2="11.5881" y2="-0.5" transform="matrix(-0.707107 0.707107 0.707107 0.707107 21.2463 12.4658)" stroke="black" />
                            </svg>
                        </DialogClose>
                    </DialogHeader>

                    <DialogBody className="px-0 py-0">
                        {/* Form */}
                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-6">
                            {/* Phone Number */}
                            <div>
                                <label htmlFor="phone" className="block text-[13px] font-medium text-black mb-1">
                                    Phone Number
                                </label>
                                <Input
                                    {...register('phone')}
                                    type="tel"
                                    id="phone"
                                    placeholder="Enter phone number"
                                    className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                                />
                                {errors.phone && (
                                    <p className="text-red-500 text-[10px] mt-1">{errors.phone.message}</p>
                                )}
                            </div>

                            {/* Phone Number */}
                            <div>
                                <label htmlFor="password" className="block text-[13px] font-medium text-black mb-1">
                                    Password
                                </label>
                                <div className="relative">
                                    <Input
                                        {...register("password")}
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="Enter your password"
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 text-black text-xs focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200 resize-none"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword((prev) => !prev)}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-gray-500 focus:outline-none"
                                        tabIndex={-1}
                                    >
                                        {showPassword ?
                                            <>
                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-.722-3.25" /><path d="M2 8a10.645 10.645 0 0 0 20 0" /><path d="m20 15-1.726-2.05" /><path d="m4 15 1.726-2.05" /><path d="m9 18 .722-3.25" /></svg>
                                            </> :
                                            <>
                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0" /><circle cx="12" cy="12" r="3" /></svg>
                                            </>}
                                    </button>
                                </div>
                                {errors.password && <p className="text-red-500 text-[10px] mt-1">{errors.password.message}</p>}
                            </div>

                            {/* Buttons */}
                            <div className='mt-10 w-full h-[1px] bg-[#E9EBEE]'></div>
                            <div className="flex space-x-3 pt-4">
                                <Button
                                    type="submit"
                                    disabled={isLoginLoading}
                                    className="bg-[#105230] hover:bg-green-700 text-white py-3 px-9 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isLoginLoading ? 'Processing...' : 'Login'}
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleCancel}
                                    className=" bg-[#E7EEEA] hover:bg-gray-300 text-[#105230] py-3 px-9 rounded-full font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </DialogBody>
                </div>
            </DialogContent>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </Dialog>
    )
}
