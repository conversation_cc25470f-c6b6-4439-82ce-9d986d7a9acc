// import { tokenlessAuthAxiosClient } from '@/lib/axios';

import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';
import { useAuth } from '@/contexts/authentication';
// import { useAuth } from '@/contexts/authentication';
import { authAxios, setAxiosDefaultToken, tokenlessAuthAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';

export interface RegisterUserDetailsDTO {
  phone_number: string
  first_name: string
  last_name: string
  password: string
  store_name: string
  email: string
  state: string
}

export interface RegisterUserDetailsResponse {
  // message: string;
  // passcode: string;
  status: string
  message: string
  user_id: number
  access: string
}

// const registerUserDetails = async (

// ) => {
//   const response = await authAxios.post(
//     '/agency/user/create_user_detail/',
//     registerUserDetailsDTO
//   );

// };

const registerUserDetails = (registerUserDetailsDTO: RegisterUserDetailsDTO): Promise<AxiosResponse<RegisterUserDetailsResponse>> =>
  tokenlessAuthAxios.post('/api/v1/user/register_user/', registerUserDetailsDTO);



export const useRegisterUserDetails = () => {
  const { authDispatch } = useAuth();

  return useMutation(registerUserDetails, {
    onSuccess: async ({ data }) => {
      const { access: token } = data;
      tokenStorage.setToken(token);
      setAxiosDefaultToken(token, authAxios);

      if (authDispatch) {
        authDispatch({ type: 'SIGNUP', payload: "SIGNUP" });

        authDispatch({ type: 'STOP_LOADING' });
      }
    },
  });
};


