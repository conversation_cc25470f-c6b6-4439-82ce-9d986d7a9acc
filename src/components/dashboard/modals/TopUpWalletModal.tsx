"use client"

import { useState } from "react"
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogClose, DialogBody } from "@/components/core/Dialog"
import { Button } from "@/components/core/Button"
import { Input } from "@/components/core/Input"

interface TopUpWalletModalProps {
  isOpen: boolean
  onClose: () => void
}

type PaymentMethod = "bank" | "card"

export default function TopUpWalletModal({ isOpen, onClose }: TopUpWalletModalProps) {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("card")

  const handleClose = () => {
    setPaymentMethod("card")
    onClose()
  }

  const handleProceed = () => {
    if (paymentMethod === "card") {
      // Handle card payment processing logic here
      console.log("Processing card payment")
    } else {
      // Handle bank transfer confirmation
      console.log("Bank transfer confirmed")
    }
    handleClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="w-full max-w-md mx-4 bg-white rounded-2xl border-0 p-0"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6">
          <DialogHeader className="flex flex-row items-center justify-between bg-transparent px-0 py-0 mb-4">
            <DialogTitle className="text-lg font-medium text-black">Wallet Top-Up</DialogTitle>
            <DialogClose>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </DialogClose>
          </DialogHeader>

          <DialogBody className="px-0 py-0">
            <div className="space-y-4">
              <p className="text-sm text-gray-600 mb-6">How would you like to fund your wallet?</p>

              {/* Payment Method Options */}
              <div className="space-y-3">
                {/* Bank Transfer Option */}
                <div
                  className={`text-black border rounded-lg cursor-pointer transition-colors ${paymentMethod === "bank"
                    ? "border-[#********] bg-green-50"
                    : "border-gray-200 hover:border-gray-300"
                    }`}
                  onClick={() => setPaymentMethod("bank")}
                >
                  <div className="flex items-center justify-between p-4">
                    <span className="text-sm font-medium">Bank Transfer</span>
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${paymentMethod === "bank" ? "border-[#105230] bg-white" : "bg-white border-[#105230]"
                        }`}
                    >
                      {paymentMethod === "bank" && (
                        <div className="w-full h-full rounded-full bg-[#105230] scale-50"></div>
                      )}
                    </div>
                  </div>

                  {/* Account Details - Show when bank is selected */}
                  {paymentMethod === "bank" && (
                    <div className="px-4 pb-4 border-gray-200 pt-4 space-y-3">
                      <p className="text-sm text-gray-600">Transfer to the account below to fund your wallet account</p>

                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Account Number</label>
                        <div className="flex items-center gap-2">
                          <span className="text-base font-semibold text-black">*************</span>
                          <Button
                            variant="outlined"
                            size="tiny"
                            className="p-1 h-auto"
                            onClick={(e) => {
                              e.stopPropagation()
                              navigator.clipboard.writeText("*************")
                            }}
                          >
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                          </Button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Account Name</label>
                        <span className="text-base font-semibold text-black">{`{Vendboss/ {Logged in user}}`}</span>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Bank</label>
                        <span className="text-base font-semibold text-black">LibertyPay MFB</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Card Payment Option */}
                <div
                  className={`flex items-center justify-between text-black p-4 border rounded-lg cursor-pointer transition-colors ${paymentMethod === "card"
                    ? "border-[#********] bg-green-50"
                    : "border-gray-200 hover:border-gray-300"
                    }`}
                  onClick={() => setPaymentMethod("card")}
                >
                  <span className="text-sm font-medium">Card Payment</span>
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${paymentMethod === "card" ? "border-[#105230] bg-white" : "bg-white border-[#105230]"
                      }`}
                  >
                    {paymentMethod === "card" && (
                      <div className="w-full h-full rounded-full bg-[#105230] scale-50"></div>
                    )}
                  </div>
                </div>
              </div>

              {/* Card Payment Form - Show when card is selected */}
              {paymentMethod === "card" && (
                <div className="mt-6 space-y-4">
                  <p className="text-sm text-gray-600 mb-4">Enter your card details to proceed with payment</p>

                  {/* Card Payment Form */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Amount</label>
                      <Input type="number" placeholder="Enter amount" className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black" />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Card Number</label>
                      <Input type="text" placeholder="1234 5678 9012 3456" className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black" />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Expiry Date</label>
                        <Input type="text" placeholder="MM/YY" className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black" />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">CVV</label>
                        <Input type="text" placeholder="123" className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black" />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Proceed Button */}
              <Button
                onClick={handleProceed}
                className="w-full bg-[#105230] hover:bg-[#0d4428] text-white py-3 rounded-lg font-medium mt-6"
              >
                {paymentMethod === "bank" ? "Confirm Transfer" : "Proceed to payment"}
              </Button>
            </div>
          </DialogBody>
        </div>
      </DialogContent>
    </Dialog>
  )
}
