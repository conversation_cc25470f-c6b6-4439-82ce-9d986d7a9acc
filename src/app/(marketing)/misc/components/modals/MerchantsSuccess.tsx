"use client"

import React from 'react'
import { useRouter } from 'next/navigation'

import {
  Dialog,
  DialogContent,
  DialogBody,
} from '@/components/core/Dialog'
import { Button } from '@/components/core';


interface StartEarningModalProps {
  isMerchantSuccessOpen: boolean;
  setMerchantSuccessModalState: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function MerchantsSuccess({ isMerchantSuccessOpen, setMerchantSuccessModalState }: StartEarningModalProps) {

  const router = useRouter()

  return (
    <Dialog open={isMerchantSuccessOpen} onOpenChange={setMerchantSuccessModalState}>
      <DialogContent
        className="w-full max-w-lg mx-4 bg-white rounded-2xl border-0 p-0 max-h-[90vh] overflow-y-auto no-scrollbar"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6">

          <DialogBody className="px-0 py-0 ">
            <div className='w-full flex items-center justify-center flex-col'>
              <svg width="116" height="116" viewBox="0 0 116 116" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.4" x="0.2" y="0.301563" width="115.6" height="114.6" rx="45.8" fill="#DAF2D0" fill-opacity="0.4" stroke="#0B3A22" strokeWidth="0.4" />
                <path d="M54.471 66.3478C53.8876 66.3478 53.3335 66.1145 52.9251 65.7061L45.8668 58.6478C45.021 57.802 45.021 56.402 45.8668 55.5561C46.7126 54.7103 48.1126 54.7103 48.9585 55.5561L54.471 61.0686L67.0126 48.527C67.8585 47.6811 69.2585 47.6811 70.1043 48.527C70.9501 49.3728 70.9501 50.7728 70.1043 51.6186L56.0168 65.7061C55.6085 66.1145 55.0543 66.3478 54.471 66.3478Z" fill="#02BA5C" />
                <path d="M57.9997 88.4554C56.1622 88.4554 54.3247 87.8429 52.8956 86.6179L48.2872 82.6512C47.8206 82.2429 46.6539 81.8346 46.0414 81.8346H41.0247C36.7081 81.8346 33.2081 78.3346 33.2081 74.0179V69.0304C33.2081 68.4179 32.7997 67.2804 32.3914 66.8137L28.4539 62.1762C26.0622 59.3471 26.0622 54.8846 28.4539 52.0554L32.3914 47.4179C32.7997 46.9512 33.2081 45.8137 33.2081 45.2012V40.1846C33.2081 35.8679 36.7081 32.3679 41.0247 32.3679H46.0706C46.6831 32.3679 47.8497 31.9304 48.3164 31.5512L52.9247 27.5846C55.7831 25.1346 60.2456 25.1346 63.1039 27.5846L67.7122 31.5512C68.1789 31.9596 69.3456 32.3679 69.9581 32.3679H74.9164C79.2331 32.3679 82.7331 35.8679 82.7331 40.1846V45.1429C82.7331 45.7554 83.1706 46.9221 83.5789 47.3887L87.5456 51.9971C89.9956 54.8554 89.9956 59.3179 87.5456 62.1762L83.5789 66.7846C83.1706 67.2512 82.7331 68.4179 82.7331 69.0304V73.9887C82.7331 78.3054 79.2331 81.8054 74.9164 81.8054H69.9581C69.3456 81.8054 68.1789 82.2429 67.7122 82.6221L63.1039 86.5887C61.6747 87.8429 59.8372 88.4554 57.9997 88.4554ZM41.0247 36.7429C39.1289 36.7429 37.5831 38.2887 37.5831 40.1846V45.1721C37.5831 46.8346 36.7956 48.9637 35.7164 50.2179L31.7789 54.8554C30.7581 56.0512 30.7581 58.1221 31.7789 59.3179L35.7164 63.9554C36.7956 65.2387 37.5831 67.3387 37.5831 69.0012V73.9887C37.5831 75.8846 39.1289 77.4304 41.0247 77.4304H46.0706C47.7622 77.4304 49.8914 78.2179 51.1747 79.3262L55.7831 83.2929C56.9789 84.3137 59.0789 84.3137 60.2747 83.2929L64.8831 79.3262C66.1664 78.2471 68.2956 77.4304 69.9872 77.4304H74.9456C76.8414 77.4304 78.3872 75.8846 78.3872 73.9887V69.0304C78.3872 67.3387 79.1747 65.2096 80.2831 63.9262L84.2497 59.3179C85.2706 58.1221 85.2706 56.0221 84.2497 54.8262L80.2831 50.2179C79.1747 48.9346 78.3872 46.8054 78.3872 45.1137V40.1846C78.3872 38.2887 76.8414 36.7429 74.9456 36.7429H69.9872C68.2956 36.7429 66.1664 35.9554 64.8831 34.8471L60.2747 30.8804C59.0789 29.8596 56.9789 29.8596 55.7831 30.8804L51.1747 34.8762C49.8914 35.9554 47.7331 36.7429 46.0706 36.7429H41.0247Z" fill="#02BA5C" />
              </svg>

              <p className='mt-6 text-[#000000] text-2xl '>
                Account created
              </p>
              <p className='mt-1 text-[#4A4A68] text-center max-w-[317px] text-xs leading-[19px]'>
                Welcome to Vendboss merchant, you can now proceed to fund your account and start making money
              </p>

              <Button
                type="submit"
                className="bg-[#105230] hover:bg-green-700 text-white py-3 px-9 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed mt-6"
                onClick={() => router.push('/dashboard')}
              >
                Proceed to dashboard
              </Button>
            </div>

          </DialogBody>
        </div>
      </DialogContent>
    </Dialog>
  )
}
