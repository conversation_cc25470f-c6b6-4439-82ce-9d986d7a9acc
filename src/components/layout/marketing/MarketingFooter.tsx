'use client';

// import Facebook from '@/app/(marketing)/landingmisc/icons/footer/Facebook';
// import Instagram from '@/app/(marketing)/landingmisc/icons/footer/Instagram';
// import Linkedln from '@/app/(marketing)/landingmisc/icons/footer/Linkedln';
// import Twitter from '@/app/(marketing)/landingmisc/icons/footer/Twitter';
// import Video from '@/app/(marketing)/landingmisc/icons/footer/Video';
// import Image from 'next/image'
import React from 'react';

export function MarketingFooter() {

    return (
        //         <footer className="w-full bg-[#0E1325] pt-[3.1875rem] ">

        //             <div className="flex items-center justify-center">
        //                 <div className="flex   gap-[1.375rem]">
        //                     <Image
        //                         alt="footerLogo"
        //                         src="/images/SeedsFooterLogo.png"
        //                         width={69}
        //                         height={69}
        //                         className='w-[4rem]  h-[4rem] mt-[1rem]'

        //                     />


        //                     <p className='text-[4.375rem] font-medium text-[#fff] '>Seeds & Pennies</p>
        //                 </div>


        //             </div>
        //             <div className=" px-[6.25rem]">

        //   <div className="flex items-center justify-between mt-[4.375rem]">
        //                 <div className="flex gap-[1rem] ">
        //                     <Twitter />
        //                     <Video />
        //                 </div>

        //             <div className="flex gap-[3.25rem]">
        //                 <p className='text-[#fff]'>About us</p>
        //                 <p className='text-[#fff]'>FAQs</p>
        //                 <p className='text-[#fff]'>contact us</p>
        //             </div>

        //             <div className="flex gap-[1rem]">
        //                 <Instagram/>
        //                 <Facebook/>
        //                 <Linkedln/>
        //             </div>

        //             </div>


        //        <div className="mt-[4.5rem] h-px w-full bg-white/20"></div>

        //             </div>


        //             <div className="flex  justify-between w-full px-[6.25rem] pb-[3.125rem]">
        //                 <p className="text-[#EAEAEA] text-sm font-normal  mt-[0.75rem]">Copyright © 2024, Seeds & Pennies. All rights reserved.</p>
        //                 <div className="flex gap-[1.5rem]">
        //                      <p className="text-[#EAEAEA] text-sm font-normal  mt-[0.75rem]">Privacy Policy</p>
        //             <p className="text-[#EAEAEA] text-sm font-normal  mt-[0.75rem]">Terms of Service</p>

        //                 </div>

        //             </div>
        //         </footer>
        <></>
    );
}