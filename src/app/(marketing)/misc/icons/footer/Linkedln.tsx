import * as React from "react";
import { SVGProps } from "react";
const Linkedln = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={20} cy={20} r={20} fill="#fff" fillOpacity={0.2} />
    <g clipPath="url(#a)">
      <path
        d="M15.783 14.168a1.667 1.667 0 1 1-3.335-.003 1.667 1.667 0 0 1 3.335.003m.05 2.9h-3.334V27.5h3.334zm5.266 0h-3.316V27.5h3.283v-5.475c0-3.05 3.975-3.334 3.975 0V27.5h3.292v-6.609c0-5.141-5.884-4.95-7.267-2.424z"
        fill="#fff"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M10 10h20v20H10z" />
      </clipPath>
    </defs>
  </svg>
);
export default Linkedln;
