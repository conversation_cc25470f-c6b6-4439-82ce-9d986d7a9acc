"use client"

import React from 'react'
import { Input } from '@/components/core/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select'
import { Button } from '@/components/core/Button'

interface InternetFormProps {
  formData: {
    accountNumber: string
    provider: string
    package: string
    phoneNumber: string
    email: string
  }
  onInputChange: (field: string, value: string) => void
  onProceed: () => void
  onCancel: () => void
}

export default function InternetForm({ formData, onInputChange, onProceed, onCancel }: InternetFormProps) {
  const getPackages = () => {
    switch (formData.provider) {
      case 'spectranet':
        return [
          { value: 'unlimited-lite', label: 'Unlimited Lite - ₦5,000', price: '5000' },
          { value: 'unlimited-classic', label: 'Unlimited Classic - ₦8,000', price: '8000' },
          { value: 'unlimited-premium', label: 'Unlimited Premium - ₦15,000', price: '15000' }
        ]
      case 'smile':
        return [
          { value: 'smallie', label: 'SmallieVoice (Unlimited) - ₦1,500', price: '1500' },
          { value: 'bigga', label: 'BiggaVoice (Unlimited) - ₦2,000', price: '2000' },
          { value: 'yakata', label: 'YakataVoice (Unlimited) - ₦5,000', price: '5000' }
        ]
      case 'swift':
        return [
          { value: 'swift-1gb', label: '1GB - ₦1,000', price: '1000' },
          { value: 'swift-2gb', label: '2GB - ₦2,000', price: '2000' },
          { value: 'swift-5gb', label: '5GB - ₦4,500', price: '4500' },
          { value: 'swift-10gb', label: '10GB - ₦8,000', price: '8000' }
        ]
      default:
        return []
    }
  }

  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-[20px] p-7">
      <div className="text-left  mb-8">
        <h2 className="text-xl font-semibold text-black mb-2">Buy Internet</h2>
        <p className="text-[#4A4A68] text-xs">Pay for internet in just 35secs</p>
      </div>

      <div className="space-y-6">
        {/* Account Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Account Number
          </label>
          <Input
            type="text"
            placeholder="Enter account number"
            value={formData.accountNumber}
            onChange={(e) => onInputChange('accountNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Internet Provider */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Internet Provider
          </label>
          <Select value={formData.provider} onValueChange={(value) => onInputChange('provider', value)}>
            <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="spectranet">Spectranet</SelectItem>
              <SelectItem value="smile">Smile</SelectItem>
              <SelectItem value="swift">Swift</SelectItem>
              <SelectItem value="ipnx">IPNX</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Package Selection */}
        {formData.provider && getPackages().length > 0 && (
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Package
            </label>
            <Select value={formData.package} onValueChange={(value) => onInputChange('package', value)}>
              <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
                <SelectValue placeholder="Select package" />
              </SelectTrigger>
              <SelectContent>
                {getPackages().map((pkg) => (
                  <SelectItem key={pkg.value} value={pkg.value}>
                    {pkg.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Phone Number
          </label>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phoneNumber}
            onChange={(e) => onInputChange('phoneNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Email (Optional)
          </label>
          <Input
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={(e) => onInputChange('email', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 pt-4">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            disabled={!formData.accountNumber || !formData.provider || !formData.phoneNumber}
          >
            Proceed
          </Button>
        </div>
      </div>
    </div>
  )
}
