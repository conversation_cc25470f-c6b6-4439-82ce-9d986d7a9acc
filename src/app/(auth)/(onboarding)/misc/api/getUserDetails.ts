
import { useQuery } from '@tanstack/react-query';
import {  UserDataVend } from '../types/users';
import { authAxios } from '@/lib/axios';

// import { UserEntities } from '../types';



export const getUserDetails = async () => {
  const response = await authAxios.get(`/api/v1/user/fetch_user_details/`);
  return response.data as UserDataVend;
};

export const useUserDetails = ({
  retryCount,
  initialData,
}: {
  retryCount?: number | boolean;
  initialData?: UserDataVend;
}) => {
  return useQuery({
    queryKey: ['user-details'],
    queryFn: getUserDetails,
    retry: retryCount ?? true,
    initialData,
    staleTime: 0, // Ensures data is always refetched when invalidated
    cacheTime: 10 * 60 * 1000, // Note: cacheTime in v4, gcTime in v5
    refetchOnWindowFocus: true, // Allows refetching on window focus
    refetchOnMount: true, // Ensures fresh data on mount
  });
};

// export const getUserDetails = async (): Promise<UserEntities> => {
//   const { data } = await authAxios.get('/agency/user/get_user_details/');
//   return data;
// };

// export const useUser = ({}) =>
//   useQuery('user-details', getAuthenticatedUser, { cacheTime: 1000 * 60 * 5 });x
