"use client"

import React from 'react'
import { Button } from '@/components/core/Button'

interface Transaction {
  id: string
  service: string
  customerId: string
  amountPaid: string
  commissionEarned: string
  paymentMethod: string
  status: 'Successful' | 'Pending' | 'Failed'
  dateTime: string
}

const sampleTransactions: Transaction[] = [
  {
    id: '001',
    service: 'Airtime',
    customerId: 'VDB29090',
    amountPaid: '₦1,400,000',
    commissionEarned: '₦1,400,000',
    paymentMethod: 'Transfer',
    status: 'Successful',
    dateTime: '9th Aug 2025, 13:52Am'
  },
  {
    id: '001',
    service: 'Electricity',
    customerId: 'VDB29090',
    amountPaid: '₦1,400,000',
    commissionEarned: 'N/A',
    paymentMethod: 'Card',
    status: 'Pending',
    dateTime: '9th Aug 2025, 13:52Am'
  },
  {
    id: '001',
    service: 'Data',
    customerId: 'VDB29090',
    amountPaid: '₦1,400,000',
    commissionEarned: '₦1,400,000',
    paymentMethod: 'Transfer',
    status: 'Failed',
    dateTime: '9th Aug 2025, 13:52Am'
  }
]

const getStatusColor = (status: Transaction['status']) => {
  switch (status) {
    case 'Successful':
      return 'text-green-600 bg-green-50'
    case 'Pending':
      return 'text-orange-600 bg-orange-50'
    case 'Failed':
      return 'text-red-600 bg-red-50'
    default:
      return 'text-gray-600 bg-gray-50'
  }
}

interface TransactionsTableProps {
  showData?: boolean
}

export default function TransactionsTable({ showData = true }: TransactionsTableProps) {
  if (!showData) {
    return null
  }

  return (
    <>
      <div className="bg-white rounded-2xl border border-gray-100 overflow-hidden">
        {/* Table Header */}
        <div className="overflow-x-auto">
          <table className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Txn ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Service
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount Paid
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commission Earned
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Method
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date/Time
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sampleTransactions.map((transaction, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.service}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.customerId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.amountPaid}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.commissionEarned}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.paymentMethod}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.dateTime}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Button
                      variant="unstyled"
                      className="text-gray-400 hover:text-gray-600 transition-colors duration-150"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
                          fill="currentColor"
                        />
                        <path
                          d="M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z"
                          fill="currentColor"
                        />
                        <path
                          d="M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z"
                          fill="currentColor"
                        />
                      </svg>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>


      <style jsx>{`
              .table-container {
        overflow-x: auto;
        max-width: 100%;
        position: relative;
      }

          .custom-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 12px;
  }

  thead th {
    position: sticky;
    top: 0;
    font-weight: 500 !important;
    text-align: left;
    font-size: 14px !important;
    background-color: #F9FAFB !important;

  }

  tbody td,
  thead th {
    border: 0.8px solid #e2e8f0;
    padding: 10px 26px;
  }

  tbody tr:nth-child(even) {
    background-color: #ffffff;
  }

  tbody tr {
    background-color: #ffffff;
  }

  tbody tr:hover {
    background-color: #ffffff;
  }

  thead tr {
  border-radius: 10px !important;
}

.scroll-container {
  position: relative;
  overflow-x: auto;
}

.scroll-arrow {
  position: absolute;
  top: 30%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: none;
  z-index: 10;
}

.scroll-arrow.left {
  left: 0;
}

.scroll-arrow.right {
  right: 0;
}

.scroll-container:hover .scroll-arrow {
  display: block;
}


  @media (max-width: 767px) {
    thead th:first-child,
    tbody td:first-child,
    thead th:nth-child(2),
    tbody td:nth-child(2),
    thead th:nth-child(3),
    tbody td:nth-child(3) {
      position: static; 
    }
  }
`}</style>
    </>
  )

}
