"use client"

import React from 'react'
import DashBoardHeader from '@/components/layout/dashboard/DashBoardHeader'
import WalletCard from '@/components/dashboard/WalletCard'
import CommissionEarnings from '@/components/dashboard/CommissionEarnings'
import ReferralSection from '@/components/dashboard/ReferralSection'
import TransactionsSection from '@/components/dashboard/TransactionsSection'

export default function MerchantDashboard() {
  return (
    <div className="min-h-screen bg-[#F9FAFB] px-4 sm:px-6 lg:px-[92px] py-[31px]">
      {/* Header */}
      <DashBoardHeader />

      {/* Main Content */}
      <main>
        {/* Top Row - Wallet, Commission, Referral in horizontal layout */}
        <div className="flex flex-col lg:flex-row gap-6 mb-8 py-[31px]">
          {/* Wallet Card */}
          <div className="lg:w-[339px] flex-shrink-0">
            <WalletCard />
          </div>

          {/* Commission Earnings */}
          <div className="flex-1">
            <CommissionEarnings />
          </div>

          {/* Referral Section */}
          <div className="lg:w-80 flex-shrink-0">
            <ReferralSection />
          </div>
        </div>

        {/* Transactions Section */}
        <div className='bg-white py-[30px] px-6 rounded-[12px] '>
          <TransactionsSection />
        </div>
      </main>
    </div>
  )
}
