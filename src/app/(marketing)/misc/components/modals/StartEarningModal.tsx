"use client"

import React from "react"
import { <PERSON>, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/core/Button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogBody,
  DialogClose,
} from "@/components/core/Dialog"
import {
  ErrorModal,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/core"

import MerchantsSuccess from "./MerchantsSuccess"

import { useBooleanStateControl } from "@/hooks"
import { useRegisterUserDetails } from "../../api/registerUserDetails"

import { AxiosError } from "axios"
import { formatAxiosErrorMessage } from "@/utils/errors"
import useErrorModalState from "@/hooks/useErrorModalState"


const nigerianStates = [
  "Abia",
  "Adamawa",
  "Akwa Ibom",
  "Anambra",
  "<PERSON>uchi",
  "Bayelsa",
  "Benue",
  "Borno",
  "Cross River",
  "Delta",
  "Ebonyi",
  "Edo",
  "Ekiti",
  "Enugu",
  "Federal Capital Territory",
  "Gombe",
  "Imo",
  "Jigawa",
  "Kaduna",
  "Kano",
  "Katsina",
  "Kebbi",
  "Kogi",
  "Kwara",
  "Lagos",
  "Nasarawa",
  "Niger",
  "Ogun",
  "Ondo",
  "Osun",
  "Oyo",
  "Plateau",
  "Rivers",
  "Sokoto",
  "Taraba",
  "Yobe",
  "Zamfara",
]

// Zod validation schema
const startEarningSchema = z.object({
  store_name: z.string()
    .trim()
    .min(1, { message: 'Please enter your store name.' }),
  first_name: z
    .string()
    .trim()
    .min(1, { message: 'Please enter your first name.' }),
  last_name: z
    .string()
    .trim()
    .min(1, { message: 'Please enter your last name.' }),
  phone_number: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^\d+$/, "Phone number must contain only digits"),
  email: z.string().email({ message: "Please enter a valid email address" }),
  state: z.string().min(1, "Please select a state"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  acceptTerms: z.boolean().refine((val) => val === true, "You must accept the terms of service"),
})

type StartEarningFormData = z.infer<typeof startEarningSchema>

interface StartEarningModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function StartEarningModal({ isOpen, onClose }: StartEarningModalProps) {

  const [showPassword, setShowPassword] = React.useState(false);
  const {
    state: isMerchantSuccessodalOpen,
    setState: setMerchantSuccessodalState,
    setTrue: openMerchantSuccessodal,
  } = useBooleanStateControl()

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    mutate: registerUserDetails,
    isPending: isRegisterUserDetailsLoading,
  } = useRegisterUserDetails();


  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<StartEarningFormData>({
    resolver: zodResolver(startEarningSchema),
  })

  const [search, setSearch] = React.useState("");

  const onSubmit = async (data: StartEarningFormData) => {
    // try {
    //   // Simulate API call
    //   await new Promise((resolve) => setTimeout(resolve, 2000))
    //   console.log("Form submitted:", data)

    //   // Reset form and close modal on success

    // } catch (error) {
    //   console.error("Submission error:", error)
    //   // Handle error (show error message)
    // }

    registerUserDetails(data,

      {
        onSuccess: () => {
          reset()
          openMerchantSuccessodal()
          onClose()
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );

  }

  const handleCancel = () => {
    reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full max-w-lg mx-4 bg-white rounded-2xl border-0 p-0 no-scrollbar"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6 max-h-[80vh] overflow-y-auto no-scrollbar">
          <DialogHeader className="text-right bg-transparent px-0 py-0">
            <DialogTitle></DialogTitle>
            <DialogClose>
              <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="17" cy="17" r="17" fill="#F9FAFB" />
                <line x1="12.8201" y1="12.1123" x2="21.0141" y2="20.3063" stroke="black" />
                <line
                  y1="-0.5"
                  x2="11.5881"
                  y2="-0.5"
                  transform="matrix(-0.707107 0.707107 0.707107 0.707107 21.2463 12.4658)"
                  stroke="black"
                />
              </svg>
            </DialogClose>
          </DialogHeader>

          <DialogBody className="px-0 py-0">
            <DialogTitle className="text-lg font-medium text-black mb-2 max-w-[393px]">
              Start Earning Passively as a Vendboss Merchant, Let your money works for you.
            </DialogTitle>
            <DialogDescription className="text-[#4A4A68] leading-[16px] text-xs max-w-[393px]">
              Earn commissions every time you or your customers buy airtime, pay electricity bills, or subscribe to
              cable TV. With Vendboss, you can grow your income effortlessly, right from your phone.
            </DialogDescription>
            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-6">
              {/* Store Name */}
              <div>
                <label htmlFor="store_name" className="block text-[13px] font-medium text-black mb-1">
                  Your Preferred Store Name
                </label>
                <Input
                  {...register("store_name")}
                  type="text"
                  id="store_name"
                  placeholder="Enter store name"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.store_name && <p className="text-red-500 text-[10px] mt-1">{errors.store_name.message}</p>}
              </div>
              <div>
                <label htmlFor="first_name" className="block text-[13px] font-medium text-black mb-1">
                  Firstname
                </label>
                <Input
                  {...register("first_name")}
                  type="text"
                  id="first_name"
                  placeholder="Enter first name"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.first_name && <p className="text-red-500 text-[10px] mt-1">{errors.first_name.message}</p>}
              </div>

              <div>
                <label htmlFor="last_name" className="block text-[13px] font-medium text-black mb-1">
                  Lastname
                </label>
                <Input
                  {...register("last_name")}
                  type="text"
                  id="last_name"
                  placeholder="Enter last name"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.last_name && <p className="text-red-500 text-[10px] mt-1">{errors.last_name.message}</p>}
              </div>

              {/* Phone Number */}
              <div>
                <label htmlFor="phone_number" className="block text-[13px] font-medium text-black mb-1">
                  Phone Number
                </label>
                <Input
                  {...register("phone_number")}
                  type="tel"
                  id="phone_number"
                  placeholder="Enter phone number"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.phone_number && <p className="text-red-500 text-[10px] mt-1">{errors.phone_number.message}</p>}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-[13px] font-medium text-black mb-1">
                  Email
                </label>
                <Input
                  {...register("email")}
                  type="email"
                  id="email"
                  placeholder="Enter email address"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.email && <p className="text-red-500 text-[10px] mt-1">{errors.email.message}</p>}
              </div>

              {/* State */}
              <div className="mt-6">
                <label className="block text-[13px] font-medium text-black mb-1">State</label>

                <Controller
                  control={control}
                  name="state"
                  render={({ field: { onChange, value, ref } }) => {


                    const filteredStates = nigerianStates?.filter((state) =>
                      state.toLowerCase().includes(search.toLowerCase())
                    );

                    return (
                      <Select value={value} onValueChange={onChange}>
                        <SelectTrigger
                          className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                          id="state"
                          ref={ref}
                        >
                          <SelectValue placeholder="Select state" />
                        </SelectTrigger>

                        <SelectContent>
                          {/* Search Input */}
                          <div className="p-2">
                            <input
                              type="text"
                              placeholder="Search state..."
                              value={search}
                              onChange={(e) => setSearch(e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
                            />
                          </div>

                          {filteredStates?.length > 0 ? (
                            filteredStates.map((state) => (
                              <SelectItem
                                key={state}
                                value={state}
                                className="bg-white text-black"
                              >
                                {state}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-xs text-gray-500">No results</div>
                          )}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />

                {errors.state && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.state.message}</p>
                )}
              </div>

              {/* BVN */}
              {/* <div>
                <label htmlFor="bvn" className="block text-[13px] font-medium text-black mb-1">
                  BVN
                </label>
                <Input
                  {...register('bvn')}
                  type="text"
                  id="bvn"
                  placeholder="Enter BVN"
                  maxLength={11}
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.bvn && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.bvn.message}</p>
                )}
              </div> */}

              {/* Address */}
              <div>
                <label htmlFor="password" className="block text-[13px] font-medium text-black mb-1">
                  Password
                </label>
                <div className="relative">
                  <Input
                    {...register("password")}
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 text-black text-xs focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200 resize-none"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword((prev) => !prev)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-gray-500 focus:outline-none"
                    tabIndex={-1}
                  >
                    {showPassword ?
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-.722-3.25" /><path d="M2 8a10.645 10.645 0 0 0 20 0" /><path d="m20 15-1.726-2.05" /><path d="m4 15 1.726-2.05" /><path d="m9 18 .722-3.25" /></svg>
                      </> :
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0" /><circle cx="12" cy="12" r="3" /></svg>
                      </>}
                  </button>
                </div>
                {errors.password && <p className="text-red-500 text-[10px] mt-1">{errors.password.message}</p>}
              </div>

              {/* Terms Checkbox */}
              <div className="flex items-center space-x-2">
                <Input
                  {...register("acceptTerms")}
                  type="checkbox"
                  id="acceptTerms"
                  className="rounded-full w-[14px] h-[14px] text-green-600 border-gray-300 focus:ring-green-500"
                />
                <label htmlFor="acceptTerms" className="text-xs text-gray-700">
                  Accept our <span className="text-[#105230]">terms of service</span>
                </label>
              </div>
              {errors.acceptTerms && <p className="text-red-500 text-[10px]">{errors.acceptTerms.message}</p>}

              {/* Buttons */}
              <div className="mt-10 w-full h-[1px] bg-[#E9EBEE]"></div>
              <div className="flex space-x-3 pt-4">
                <Button
                  type="submit"
                  disabled={isRegisterUserDetailsLoading}
                  className="bg-[#105230] hover:bg-green-700 text-white py-3 px-9 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRegisterUserDetailsLoading ? "Processing..." : "Proceed"}
                </Button>
                <Button
                  type="button"
                  onClick={handleCancel}
                  className=" bg-[#E7EEEA] hover:bg-gray-300 text-[#105230] py-3 px-9 rounded-full font-medium transition-colors duration-200"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogBody>
        </div>
      </DialogContent>

      <MerchantsSuccess
        isMerchantSuccessOpen={isMerchantSuccessodalOpen}
        setMerchantSuccessModalState={setMerchantSuccessodalState}
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  )
}
