"use client"

import React from 'react'
import { Input } from '@/components/core/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select'
import { Button } from '@/components/core/Button'

interface CableTVFormProps {
  formData: {
    decoderNumber: string
    provider: string
    package: string
    phoneNumber: string
    email: string
  }
  onInputChange: (field: string, value: string) => void
  onProceed: () => void
  onCancel: () => void
}

export default function CableTVForm({ formData, onInputChange, onProceed, onCancel }: CableTVFormProps) {
  const getPackages = () => {
    switch (formData.provider) {
      case 'dstv':
        return [
          { value: 'dstv-padi', label: 'DStv Padi - ₦2,150', price: '2150' },
          { value: 'dstv-yanga', label: 'DStv Yanga - ₦2,950', price: '2950' },
          { value: 'dstv-confam', label: 'DStv Confam - ₦5,300', price: '5300' },
          { value: 'dstv-compact', label: 'DStv Compact - ₦9,000', price: '9000' },
          { value: 'dstv-compact-plus', label: 'DStv Compact Plus - ₦14,250', price: '14250' },
          { value: 'dstv-premium', label: 'DStv Premium - ₦21,000', price: '21000' }
        ]
      case 'gotv':
        return [
          { value: 'gotv-smallie', label: 'GOtv Smallie - ₦900', price: '900' },
          { value: 'gotv-jinja', label: 'GOtv Jinja - ₦1,900', price: '1900' },
          { value: 'gotv-jolli', label: 'GOtv Jolli - ₦2,800', price: '2800' },
          { value: 'gotv-max', label: 'GOtv Max - ₦4,150', price: '4150' }
        ]
      case 'startimes':
        return [
          { value: 'nova', label: 'Nova - ₦900', price: '900' },
          { value: 'basic', label: 'Basic - ₦1,800', price: '1800' },
          { value: 'smart', label: 'Smart - ₦2,600', price: '2600' },
          { value: 'classic', label: 'Classic - ₦2,750', price: '2750' },
          { value: 'super', label: 'Super - ₦4,900', price: '4900' }
        ]
      default:
        return []
    }
  }

  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-[20px] p-7">
      <div className="text-left  mb-8">
        <h2 className="text-xl font-semibold text-black mb-2">Buy Cable TV</h2>
        <p className="text-[#4A4A68] text-xs">Pay for cable TV in just 35secs</p>
      </div>

      <div className="space-y-6">
        {/* Decoder Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Decoder Number
          </label>
          <Input
            type="text"
            placeholder="Enter decoder number"
            value={formData.decoderNumber}
            onChange={(e) => onInputChange('decoderNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Cable Provider */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Cable Provider
          </label>
          <Select value={formData.provider} onValueChange={(value) => onInputChange('provider', value)}>
            <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dstv">DStv</SelectItem>
              <SelectItem value="gotv">GOtv</SelectItem>
              <SelectItem value="startimes">StarTimes</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Package Selection */}
        {formData.provider && (
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Package
            </label>
            <Select value={formData.package} onValueChange={(value) => onInputChange('package', value)}>
              <SelectTrigger className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black">
                <SelectValue placeholder="Select package" />
              </SelectTrigger>
              <SelectContent>
                {getPackages().map((pkg) => (
                  <SelectItem key={pkg.value} value={pkg.value}>
                    {pkg.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Phone Number
          </label>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phoneNumber}
            onChange={(e) => onInputChange('phoneNumber', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-black mb-2">
            Email (Optional)
          </label>
          <Input
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={(e) => onInputChange('email', e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded-md text-[10px] focus:outline-none focus:ring-1 focus:ring-green-500 text-black"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 pt-4">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={onProceed}
            className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            disabled={!formData.decoderNumber || !formData.provider || !formData.package || !formData.phoneNumber}
          >
            Proceed
          </Button>
        </div>
      </div>
    </div>
  )
}
