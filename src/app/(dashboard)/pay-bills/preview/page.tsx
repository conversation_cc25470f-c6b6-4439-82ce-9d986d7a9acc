"use client"

import React from 'react'
import DashBoardHeader from '@/components/layout/dashboard/DashBoardHeader'
import { Button } from '@/components/core/Button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { usePayBills } from '@/contexts/PayBillsContext'

export default function PreviewPurchasePage() {
  const router = useRouter()
  const { activeService, getCurrentFormData } = usePayBills()

  const handleBack = () => {
    router.back()
  }

  const handleProceedToPayment = () => {
    router.push('/pay-bills/success')
  }

  const handleCancel = () => {
    router.push('/pay-bills')
  }

  const formData = getCurrentFormData()

  // Mock data - in real app this would come from API based on form data
  const purchaseData = {
    service: activeService.toLowerCase(),
    meterDetails: {
      meterNumber: formData.meterNumber || formData.decoderNumber || formData.accountNumber || formData.serviceId || '**************',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Oluwafkayomi',
      meterType: formData.meterType || formData.provider || 'Prepaid',
      disco: formData.location || formData.provider || 'Port-Harcourt',
      phone: formData.phoneNumber || '***********',
      address: 'No 15 Awolowo Raod, Old GRA, Phase II, PH',
      outstanding: '₦0.00'
    },
    paymentBreakdown: {
      vendAmount: formData.amount ? `₦${formData.amount}` : '₦0.00',
      serviceCharge: '₦0.00',
      gatewayCharge: '₦0.00',
      discount: '₦0.00',
      total: formData.amount ? `₦${formData.amount}` : '₦0.00'
    }
  }

  return (
    <div className="min-h-screen bg-[#F9FAFB] px-4 sm:px-6 lg:px-[92px]">
      {/* Header */}
      <DashBoardHeader />

      {/* Main Content */}
      <main className="py-8">
        {/* Top Navigation */}
        <div className="bg-white rounded-lg mb-6">
          <div className="flex items-center justify-between px-6 py-4 border-b">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="font-medium text-gray-900">VENDBOSS</span>
              </div>
              <nav className="flex space-x-8">
                <a href="#" className="text-gray-900 font-medium border-b-2 border-green-600 pb-1">Pay Bills</a>
                <a href="#" className="text-gray-500 hover:text-gray-900">Transaction History</a>
                <a href="#" className="text-gray-500 hover:text-gray-900">Notifications</a>
              </nav>
            </div>
            <div className="flex items-center space-x-6">
              <a href="#" className="text-gray-500 hover:text-gray-900">Merchants</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">FAQ</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">Contacts</a>
            </div>
          </div>
        </div>

        {/* Preview Content */}
        <div className="bg-white rounded-lg p-6 max-w-2xl mx-auto">
          {/* Back Button */}
          <button
            onClick={handleBack}
            className="flex items-center text-green-600 hover:text-green-700 mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>

          {/* Header */}
          <div className="text-left  mb-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">Preview purchase</h1>
            <p className="text-[#4A4A68] text-xs">Pay for {activeService.toLowerCase()} in just 35secs</p>
          </div>

          {/* Meter Details */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Meter Details</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Meter Number</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.meterNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Name</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Meter Type</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.meterType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Disco</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.disco}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phone</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.phone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Address</span>
                <span className="text-gray-900 font-medium text-right max-w-xs">
                  {purchaseData.meterDetails.address}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Outstanding</span>
                <span className="text-gray-900 font-medium">{purchaseData.meterDetails.outstanding}</span>
              </div>
            </div>
          </div>

          {/* Payment Breakdown */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Breakdown</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Vend Amount</span>
                <span className="text-gray-900 font-medium">{purchaseData.paymentBreakdown.vendAmount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Service Charge</span>
                <span className="text-gray-900 font-medium">{purchaseData.paymentBreakdown.serviceCharge}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Gateway Charge</span>
                <span className="text-gray-900 font-medium">{purchaseData.paymentBreakdown.gatewayCharge}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount</span>
                <span className="text-gray-900 font-medium">{purchaseData.paymentBreakdown.discount}</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total</span>
                  <span className="text-lg font-semibold text-gray-900">{purchaseData.paymentBreakdown.total}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4">
            <Button
              onClick={handleCancel}
              className="flex-1 py-3 text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleProceedToPayment}
              className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white"
            >
              Proceed to payment
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
