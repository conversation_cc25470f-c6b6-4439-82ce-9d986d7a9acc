"use client"

import React from 'react'
import DashBoardHeader from '@/components/layout/dashboard/DashBoardHeader'
import { But<PERSON> } from '@/components/core/Button'
import { CheckCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { usePayBills } from '@/contexts/PayBillsContext'

export default function PurchaseSuccessPage() {
  const router = useRouter()
  const { activeService, getCurrentFormData, resetCurrentFormData } = usePayBills()

  const handleMakeAnotherPurchase = () => {
    resetCurrentFormData()
    router.push('/pay-bills')
  }

  const handleReturnHome = () => {
    router.push('/dashboard')
  }

  const formData = getCurrentFormData()

  // Mock transaction data - in real app this would come from the actual transaction
  const transactionData = {
    amount: formData.amount || '200',
    currency: '₦',
    transactionId: 'N383,384',
    service: activeService.toLowerCase()
  }

  return (
    <div className="min-h-screen bg-[#F9FAFB] px-4 sm:px-6 lg:px-[92px]">
      {/* Header */}
      <DashBoardHeader />

      {/* Main Content */}
      <main className="py-8">
        {/* Top Navigation */}
        <div className="bg-white rounded-lg mb-6">
          <div className="flex items-center justify-between px-6 py-4 border-b">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="font-medium text-gray-900">VENDBOSS</span>
              </div>
              <nav className="flex space-x-8">
                <a href="#" className="text-gray-900 font-medium border-b-2 border-green-600 pb-1">Pay Bills</a>
                <a href="#" className="text-gray-500 hover:text-gray-900">Transaction History</a>
                <a href="#" className="text-gray-500 hover:text-gray-900">Notifications</a>
              </nav>
            </div>
            <div className="flex items-center space-x-6">
              <a href="#" className="text-gray-500 hover:text-gray-900">Merchants</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">FAQ</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">Contacts</a>
            </div>
          </div>
        </div>

        {/* Success Content */}
        <div className="bg-white rounded-lg p-8 max-w-md mx-auto text-center">
          {/* Success Icon */}
          <div className="mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>

          {/* Success Message */}
          <div className="mb-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-4">Purchase successful</h1>
            <p className="text-gray-600 mb-2">
              You have successfully purchase {transactionData.amount} units for {transactionData.currency}{transactionData.transactionId}.
            </p>
            <p className="text-[#4A4A68] text-xs">
              Please your email box or sms for a token sent to you.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Button
              onClick={handleMakeAnotherPurchase}
              className="w-full py-3 bg-green-600 hover:bg-green-700 text-white font-medium"
            >
              Make another purchase
            </Button>
            <button
              onClick={handleReturnHome}
              className="w-full py-3 text-gray-600 hover:text-gray-800 font-medium bg-transparent border-none"
            >
              Return home
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}
