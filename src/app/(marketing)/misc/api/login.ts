import { useAuth } from '@/contexts/authentication';
import { authAxios, setAxiosDefaultToken, tokenlessAuthAxios } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';

import type { AxiosResponse } from 'axios';


import { getUserDetails } from '@/app/(auth)/(onboarding)/misc/api';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc/utils';
import { LoginDto } from '@/app/(auth)/(onboarding)/misc';



interface TokenResponse {
  access: string;
  refresh: string;
}

const login = (loginDto: LoginDto): Promise<AxiosResponse<TokenResponse>> =>
  tokenlessAuthAxios.post('/api/v1/user/phone_signin/', loginDto);

export const useLogin = () => {
  const { authDispatch } = useAuth();

  return useMutation(login, {
    onSuccess: async ({ data }) => {
      const { access: token } = data;

      tokenStorage.setToken(token);
      setAxiosDefaultToken(token, authAxios);

      const user = await getUserDetails();

      if (authDispatch) {
        authDispatch({ type: 'LOGIN', payload: user });

        authDispatch({ type: 'STOP_LOADING' });
      }
    },
  });
};

// export const useLogin = () => {
//   const { authDispatch } = useAuth();
//   useMutation({});
//   return useMutation({
//     mutationFn: login,
//     onSuccess: async ({ data }) => {
//       const { access: token } = data;

//       tokenStorage.setToken(token);
//       setAxiosDefaultToken(token, authAxios);
//       setAxiosDefaultToken(token, managementAxios);

//       const user = await getUserDetails();

//       if (authDispatch) {
//         authDispatch({ type: 'LOGIN', payload: user });

//         authDispatch({ type: 'STOP_LOADING' });
//       }
//     },
//   });
// };
