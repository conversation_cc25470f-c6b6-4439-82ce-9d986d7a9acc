'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import { cn } from '@/utils/classNames';
import { linkGroups } from './DesktopMenuBar';
import { Button } from '@/components/core/Button';
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/core/Dialog';



export function MobileMenuDialog() {
  const _pathname = usePathname();

  return (
    <div className="md:hidden">
      <Dialog>
        <DialogTrigger asChild>
          <Button className={cn("text-[#F9F9F9] bg-[#460666] px-[1.8125rem] py-[0.5rem] rounded-[10px] hover:bg-[#5a0d7a] transition-colors", "font-display")}>
            Menu
          </Button>
        </DialogTrigger>
        <DialogContent className="w-full max-w-sm mx-auto bg-[#460666] border-[#460666] text-white rounded-lg shadow-xl">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-white">Menu</h2>
              <DialogClose className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </DialogClose>
            </div>

            <nav className="space-y-4">
              {linkGroups.map((link, index) => {
                const isActive = _pathname === link.link;

                return (
                  <DialogClose key={index} asChild>
                    <Link
                      href={link.link}
                      className={cn(
                        "block px-4 py-3 text-base font-medium rounded-lg transition-colors border-b border-white/20",
                        isActive
                          ? "bg-white/20 text-white font-semibold"
                          : "hover:bg-white/10 text-white/90 hover:text-white"
                      )}
                    >
                      {link.text}
                    </Link>
                  </DialogClose>
                );
              })}
            </nav>

            <div className="mt-6 pt-6 border-t border-white/20 space-y-4">
              <DialogClose asChild>
                <Link href="/login" className="block px-4 py-2 text-sm font-medium text-white/80 hover:text-white transition-colors">
                  Login
                </Link>
              </DialogClose>
              <DialogClose asChild>
                <button className="w-full flex justify-center items-center bg-[#2E6CE7] text-white rounded-lg text-sm py-3 px-6 font-medium hover:bg-[#2557c7] transition-colors">
                  Apply Now
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </DialogClose>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
