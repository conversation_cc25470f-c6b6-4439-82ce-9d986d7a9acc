'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import { <PERSON>ton, Dialog, DialogBody, DialogClose, DialogContent, DialogHeader, DialogTitle, FormError, Input } from '@/components/core';
import { addCommasToNumber } from '@/utils/numbers';



// import { WithdrawFundTransactionPinDialog } from './WithdrawTransactionPinDialog';

const RegistrationModalFormSchema = z.object({
  amount: z
    .string({ error: 'Please enter an amount.' })
    .regex(/^\d+$/, { message: 'This must be a number.' })
    .trim(),
});

export type RegistrationModalFormValues = z.infer<typeof RegistrationModalFormSchema>;
interface WithdrawSavingsFundProps {
  isRegistrationModal: boolean;
  plan: string;
  saved_amount: number;
  saving_id: number;
  savings_type: string;
  email: string;
  setRegistrationModal: React.Dispatch<React.SetStateAction<boolean>>;
}
export function RegistrationModal({
  plan,
  saved_amount,

  isRegistrationModal,
  setRegistrationModal,
}: WithdrawSavingsFundProps) {


  const {
    control,
    handleSubmit,
    register,

    formState: { errors },
  } = useForm<RegistrationModalFormValues>({
    resolver: zodResolver(RegistrationModalFormSchema),
    defaultValues: {
      amount: undefined,
    },
  });

  const amountValue = useWatch({
    control,
    name: 'amount',
  });

  const onWithdrawFundsSubmit = () => {

  };

  return (
    <Dialog
      open={isRegistrationModal}
      onOpenChange={setRegistrationModal}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Withdraw</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="px-[34px]">
          <div className="rounded-lg bg-[#F5F8FF] px-6 py-[17px]">
            <p className="text-xs font-normal leading-[27px] text-[#354D99]">
              {plan.charAt(0).toUpperCase() + plan.slice(1)}
            </p>
            <h3 className="font-heading text-xl font-semibold text-[#032282]">
              ₦{addCommasToNumber(Number(saved_amount))}.00
            </h3>
          </div>
          <div className="my-6 flex items-center justify-between">
            <div>
              <p className="text-xs text-[#556575]">Transfer from:</p>
              <p className="font-heading text-sm font-semibold text-black">
                Savings Wallet
              </p>
            </div>
            <div>
              <svg
                fill="none"
                height="20"
                viewBox="0 0 20 20"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.14988 5.15834L6.05824 2.06665C5.99991 2.00832 5.9249 1.95833 5.84157 1.925C5.83324 1.925 5.82488 1.92499 5.81654 1.91666C5.74988 1.89166 5.67487 1.875 5.59987 1.875C5.4332 1.875 5.2749 1.94164 5.15823 2.05831L2.0582 5.15834C1.81654 5.4 1.81654 5.8 2.0582 6.04167C2.29987 6.28333 2.69992 6.28333 2.94159 6.04167L4.98321 3.99999V17.5C4.98321 17.8417 5.26654 18.125 5.60821 18.125C5.94988 18.125 6.23321 17.8417 6.23321 17.5V4.00833L8.26655 6.04167C8.39155 6.16667 8.5499 6.22498 8.70824 6.22498C8.86657 6.22498 9.02488 6.16667 9.14988 6.04167C9.39154 5.8 9.39154 5.40834 9.14988 5.15834Z"
                  fill="#292D32"
                />
                <path
                  d="M17.9416 13.9583C17.7 13.7167 17.2999 13.7167 17.0582 13.9583L15.0166 16V2.5C15.0166 2.15833 14.7333 1.875 14.3916 1.875C14.05 1.875 13.7666 2.15833 13.7666 2.5V15.9917L11.7333 13.9583C11.4916 13.7167 11.0916 13.7167 10.85 13.9583C10.6083 14.2 10.6083 14.6 10.85 14.8417L13.9416 17.9333C13.9999 17.9917 14.0749 18.0417 14.1583 18.075C14.1666 18.075 14.175 18.075 14.1833 18.0833C14.25 18.1083 14.325 18.125 14.4 18.125C14.5666 18.125 14.7249 18.0584 14.8416 17.9417L17.9416 14.8417C18.1833 14.5917 18.1833 14.2 17.9416 13.9583Z"
                  fill="#292D32"
                  opacity="0.4"
                />
              </svg>
            </div>
            <div>
              <p className="text-xs text-[#556575]">Transfer to:</p>
              <p className="font-heading text-sm font-semibold text-black">
                Main Wallet
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit(onWithdrawFundsSubmit)}>
            <div className="mb-6">
              <p className="mb-2 text-sm font-normal text-[#032282]">Amount</p>
              <Input
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect="off"
                id="text"
                placeholder="Enter amount"
                type="text"
                {...register('amount')}
              />

              {errors?.amount && (
                <FormError errorMessage={errors.amount.message} />
              )}
            </div>

            <Button
              className="relative flex py-3 text-sm"
              disabled={!amountValue}
              size="fullWidth"
              type="submit"
            >
              <span className="inline-block">Continue</span>

              <span className="absolute right-4 inline-block rounded-[5px] border-none bg-[#062996] px-2 py-1">
                ₦{addCommasToNumber(Number(amountValue))}.00
              </span>
            </Button>
          </form>
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
